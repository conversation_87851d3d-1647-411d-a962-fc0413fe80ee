class_name SpaceCamera
extends Camera2D

signal zoom_changed

const DEFAULT_TRANSITION_TIME: float = 0.5
# How many percent (normalized between 0.0-1.0) of the world should be visible
# at full zoom out.
const LEVEL_SIZE_MODIFIER: float = 0.8

@export_category("Camera Parameters")
@export var release_falloff: int = 35
@export var acceleration: int = 100
@export var max_speed: int = 20

@export_category("Zoom Configuration")
@export var max_zoom_out: float = 10.0
@export var max_zoom_in: float = 1.0

@export_category("Camera Boundary")
@export var border: ReferenceRect

var default_zoom: Vector2 = Vector2.ZERO
var velocity: Vector2 = Vector2.ZERO

var dragging: bool = false
var drag_mouse_origin: Vector2 = Vector2.ZERO
var drag_camera_origin: Vector2 = Vector2.ZERO

var last_position: Vector2
var last_zoom: Vector2

var zoom_tween: Tween
var move_tween: Tween

@onready var camera_area: CollisionShape2D = $"Area2D/CollisionShape2D"


func _ready() -> void:
	PlanetSignalBus.planet_menu_opened.connect(_on_planet_menu_opened)
	PlanetSignalBus.planet_menu_close.connect(_on_planet_menu_close)


func _on_planet_menu_opened(planet: PlanetNode) -> void:
	if move_tween != null and move_tween.is_running():
		# planet to planet transition
		pass
	else:
		# zoom out to planet transition
		last_position = global_position

	if zoom_tween != null and zoom_tween.is_running():
		pass
	else:
		last_zoom = get_zoom()

	smooth_center_camera_on(planet.global_position)
	smooth_camera_zoom(default_zoom)


func _on_planet_menu_close(_planet: PlanetNode) -> void:
	smooth_center_camera_on(last_position)
	smooth_camera_zoom(last_zoom)


func _on_border_resized() -> void:
	# Set correct zoom
	var zoom_vector := get_camera_zoom_to_map()
	default_zoom = zoom_vector * 2
	set_zoom(default_zoom)


func center_camera_on(pos: Vector2) -> void:
	var max_viewport_size: Vector2 = get_viewport_to_zoom_scale()
	if anchor_mode == Camera2D.ANCHOR_MODE_FIXED_TOP_LEFT:
		global_position = pos - max_viewport_size / 2
	else:
		global_position = pos


## Smoothly centers the camera on target position in set time.
func smooth_center_camera_on(target_position: Vector2, transition_time: float = DEFAULT_TRANSITION_TIME) -> void:
	if move_tween == null or not move_tween.is_valid():
		move_tween = create_tween()
	move_tween.chain()
	move_tween.set_loops(1)
	move_tween.set_parallel(true)
	move_tween.set_ease(Tween.EaseType.EASE_OUT)
	move_tween.set_trans(Tween.TransitionType.TRANS_QUAD)
	move_tween.tween_method(center_camera_on, global_position, target_position, transition_time)


## Smoothly zooms to the target zoom in set time.
func smooth_camera_zoom(target_zoom: Vector2, transition_time: float = DEFAULT_TRANSITION_TIME) -> void:
	if zoom_tween == null or not zoom_tween.is_valid():
		zoom_tween = create_tween()
	zoom_tween.chain()
	zoom_tween.set_loops(1)
	zoom_tween.set_parallel(true)
	zoom_tween.set_ease(Tween.EaseType.EASE_OUT)
	zoom_tween.set_trans(Tween.TransitionType.TRANS_QUAD)
	zoom_tween.tween_method(set_zoom, get_zoom(), target_zoom, transition_time)


func _physics_process(delta: float) -> void:
	# Normal camera movement via input
	var input_vector: Vector2 = Input.get_vector(
		"camera_move_left", "camera_move_right", "camera_move_up", "camera_move_down"
	)

	# Zoom input
	var zoom_diff: float = 0.0
	if Input.is_action_just_pressed(&"zoom_in"):
		zoom_diff += 0.1
	if Input.is_action_just_pressed(&"zoom_out"):
		zoom_diff -= 0.1

	_update_collision_area()
	zoom_changed.emit()

	var current_zoom: Vector2 = get_zoom()
	var new_zoom: Vector2 = Vector2(
		clampf(current_zoom.x + zoom_diff, max_zoom_out, max_zoom_in),
		clampf(current_zoom.y + zoom_diff, max_zoom_out, max_zoom_in)
	)
	set_zoom(new_zoom)

	calculate_velocity(input_vector, delta)
	update_global_position(delta)

	# Middle mouse drag movement
	if Input.is_mouse_button_pressed(MOUSE_BUTTON_MIDDLE):
		if not dragging:
			# First frame: save both the mouse's world position and the camera's current position
			drag_mouse_origin = get_viewport().get_mouse_position()
			drag_camera_origin = global_position
			dragging = true
		else:
			# Compute offset from original mouse pos and set position based on that offset
			var drag_current: Vector2 = get_viewport().get_mouse_position()
			var offset_current: Vector2 = drag_mouse_origin - drag_current
			global_position = limit_pos_to_screen(drag_camera_origin + offset_current)
	else:
		dragging = false


## Changes the current zoom by provided percent.[br]
## The percent value is reversed so that it is more intuitive, i.e. positive zoom zooms in
## and negative zoom zooms out.
func change_zoom(change_percent: float) -> void:
	# We take the difference between max and min zoom and then factor it by provided percent.
	var zoom_diff: float = (max_zoom_out - max_zoom_in) * -change_percent
	var current_zoom: Vector2 = get_zoom()
	var new_zoom: Vector2 = Vector2(
		clampf(current_zoom.x + zoom_diff, max_zoom_out, max_zoom_in),
		clampf(current_zoom.y + zoom_diff, max_zoom_out, max_zoom_in)
	)
	set_zoom(new_zoom)


## Gets the rectangle that wraps around current view.
func get_camera_rect() -> Rect2:
	var pos: Vector2 = get_screen_center_position()
	var half_size: Vector2 = (get_viewport_rect().size / zoom) * 0.5
	return Rect2(pos - half_size, get_viewport_rect().size / zoom)


func _update_collision_area() -> void:
	camera_area.shape.get_rect().size = get_camera_rect().size


func update_global_position(delta: float) -> void:
	global_position += lerp(velocity, Vector2.ZERO, pow(2, -32 * delta))
	global_position = limit_pos_to_screen(global_position)


func limit_pos_to_screen(pos: Vector2) -> Vector2:
	var zoomed_viewport_size: Vector2 = get_viewport_to_zoom_scale()

	# Restricts camera movement to border rectangle - size of viewport
	var left_limit: float = border.global_position.x + zoomed_viewport_size.x / 2
	var right_limit: float = border.global_position.x + border.size.x - zoomed_viewport_size.x / 2
	var top_limit: float = border.global_position.y + zoomed_viewport_size.y / 2
	var bottom_limit: float = border.global_position.y + border.size.y - zoomed_viewport_size.y / 2

	# Calculates offset for centering the camera
	var pos_offset: Vector2 = Vector2.ZERO
	if anchor_mode != Camera2D.ANCHOR_MODE_DRAG_CENTER:
		pos_offset = zoomed_viewport_size / 2

	# Clamps position of camera
	var x_pos: float = clamp(pos.x + pos_offset.x, left_limit, right_limit)
	var y_pos: float = clamp(pos.y + pos_offset.y, top_limit, bottom_limit)

	# Handle edge case where camera does not fit required area
	if left_limit > right_limit:
		x_pos = (left_limit + right_limit) / 2
	if top_limit > bottom_limit:
		y_pos = (top_limit + bottom_limit) / 2

	return Vector2(x_pos - pos_offset.x, y_pos - pos_offset.y)


func get_viewport_to_zoom_scale() -> Vector2:
	var zoom_vector: Vector2 = get_zoom()
	var zoomed_viewport_size: Vector2 = Vector2(
		get_viewport().get_visible_rect().size[0] / zoom_vector.x,
		get_viewport().get_visible_rect().size[1] / zoom_vector.y)
	return zoomed_viewport_size


func calculate_velocity(direction: Vector2, delta: float) -> void:
	velocity += direction * acceleration * delta

	if direction.x == 0:
		velocity.x = lerp(0.0, velocity.x, pow(2, -release_falloff * delta))
	if direction.y == 0:
		velocity.y = lerp(0.0, velocity.y, pow(2, -release_falloff * delta))

	velocity.x = clamp(velocity.x, -max_speed, max_speed)

	velocity.y = clamp(velocity.y, -max_speed, max_speed)


func get_camera_zoom_to_map() -> Vector2:
	var viewport_size: Vector2 = get_viewport().get_visible_rect().size  # [x, y]

	var viewport_aspect: float = viewport_size[0] / viewport_size[1]
	# We do not want to allow to zoom out and see the whole level, only a part of it.
	# Therefore, we decrease the world size of this computation so that we are able to
	# see only some fraction of it full zoom out.
	var level_size: Vector2 = border.size * LEVEL_SIZE_MODIFIER
	var level_aspect: float = float(level_size.x) / level_size.y

	var new_zoom: float = 1.0

	if level_aspect > viewport_aspect:
		new_zoom = float(viewport_size[1]) / level_size.y
	else:
		new_zoom = float(viewport_size[0]) / level_size.x

	LogManager.info("Calculated max zoom %f, base camera limits <%f %f>" % [new_zoom, max_zoom_in, max_zoom_out], self)
	max_zoom_out = min(new_zoom, max_zoom_out)
	new_zoom = clamp(new_zoom, max_zoom_out, max_zoom_in)

	return Vector2(new_zoom, new_zoom)

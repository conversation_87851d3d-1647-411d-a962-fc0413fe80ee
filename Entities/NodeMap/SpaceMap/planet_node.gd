class_name PlanetNode
extends UnlockableNode

@export var planet_scene: PackedScene
@export var high_res_animation: Sprite<PERSON>rames
@export var low_res_animation: SpriteFrames
## List of win conditions not the final ones
## These are used for their data to rebuild actuall win conditions
@export var win_conditions: Array[WinConditionData]

@onready var selected_menu: SelectedPlanetMenu = $SelectedPlanetMenu
@onready var selected_menu_animation_player: AnimationPlayer = selected_menu.animation_player
@onready var animated_sprite: AnimatedSprite2D = $AnimatedSprite2D


func _ready() -> void:
	super._ready()
	PlanetSignalBus.planet_menu_opening.connect(_on_planet_menu_opening)
	selected_menu.go_to_planet.connect(_on_go_to_planet_button_pressed)
	selected_menu.close_the_menu.connect(_on_close_button_pressed)
	animated_sprite.sprite_frames = low_res_animation
	selected_menu.set_animation(high_res_animation)
	selected_menu.set_win_data(win_conditions)


func _on_area_2d_input_event(_viewport: Node, event: InputEvent, _shape_idx: int) -> void:
	if event is InputEventMouseButton and (event as InputEventMouseButton).button_index == MOUSE_BUTTON_LEFT and (event as InputEventMouseButton).pressed:
		match node_state:
			NodeState.Enum.LOCKED:
				pass
			NodeState.Enum.UNLOCKED:
				_show_menu()
			NodeState.Enum.BEATEN:
				_show_menu()


func _on_planet_menu_opening(_emitter: Node) -> void:
	# Closes planet menu before selected one opens
	if selected_menu.visible:
		_hide_menu()


func _show_menu() -> void:
	PlanetSignalBus.planet_menu_opening.emit(self)

	selected_menu.show()
	selected_menu_animation_player.play("ShowMenu")

	PlanetSignalBus.planet_menu_opened.emit(self)


func _hide_menu() -> void:
	selected_menu_animation_player.play_backwards("ShowMenu")
	selected_menu_animation_player.animation_finished.connect(
		func(_anim_name: StringName) -> void: selected_menu.hide(), CONNECT_ONE_SHOT
	)

	PlanetSignalBus.planet_menu_close.emit(self)


func _load_planet() -> void:
	if planet_scene != null:
		SceneManager.to_planet_surface(planet_scene)

	# TODO some logic here to trigger
	if node_state < NodeState.Enum.BEATEN:
		set_state(NodeState.Enum.BEATEN)


func _on_go_to_planet_button_pressed() -> void:
	_load_planet()


func _on_close_button_pressed() -> void:
	_hide_menu()

@icon("res://assets/IconGodotNode/node_2D/icon_bone.png")

class_name Item
extends Node2D

## Emitted when item stats were successfully reloaded.
signal stats_reloaded
## Emitted when the movement of an item is finished.
signal movement_finished

@export var item_data: ItemData

var temperature: float
var tween: Tween

@onready var item_heat_glow: Sprite2D = $ItemHeatGlow
@onready var item_sprite: Sprite2D = $ItemSprite


func _ready() -> void:
	reload_stats()


func move_to(
	target_position: Vector2, transition_time: float = 0.0, skip_animation: bool = false, base_tween: Tween = null
) -> void:
	if skip_animation or transition_time <= 0:
		global_position = target_position
		global_rotation_degrees = 0
		movement_finished.emit()
		return

	if base_tween == null:
		tween = create_tween()
	else:
		tween = base_tween

	# This has to be set to 1, otherwise the finished signal is never going to be emitted
	tween.set_loops(1)
	tween.finished.connect(func() -> void: movement_finished.emit())
	tween.tween_property(self, "global_position", target_position, transition_time)


func is_moving() -> bool:
	return tween != null and tween.is_running()


func reload_stats() -> void:
	if item_data == null:
		push_error("No item data was passed")
		return

	item_sprite.texture = item_data.sprite
	temperature = item_data.temperature

	stats_reloaded.emit()


func _physics_process(delta: float) -> void:
	(item_sprite.material as ShaderMaterial).set_shader_parameter("temperature", temperature)
	(item_heat_glow.material as ShaderMaterial).set_shader_parameter("temperature", temperature)

	_simulate_cooling(delta)


## Simulates material cooling down
func _simulate_cooling(delta: float) -> void:
	const AMBIENT_TEMPERATURE: int = 20
	const COOLING_COEFFICIENT: float = 1.0 / 10

	if temperature <= AMBIENT_TEMPERATURE + 1:
		return

	temperature = lerpf(temperature, AMBIENT_TEMPERATURE, COOLING_COEFFICIENT * delta)

[gd_resource type="Resource" script_class="ItemRecipe" load_steps=7 format=3 uid="uid://e8ddhdoh77g3"]

[ext_resource type="Script" uid="uid://dta70er7xbp1f" path="res://Entities/Items/item_data.gd" id="1_x8ws1"]
[ext_resource type="Script" uid="uid://kiuh738llx2t" path="res://Entities/Items/item_recipe.gd" id="2_d7o1h"]
[ext_resource type="Texture2D" uid="uid://by0y44x5wge6r" path="res://assets/Sprites/32x32/SpriteSheets/Refined_items.png" id="2_nyli1"]
[ext_resource type="Resource" uid="uid://b2ynblax4o2hx" path="res://Entities/Items/Resources/Ores/cloud_ore.tres" id="2_tf2lj"]
[ext_resource type="Resource" uid="uid://544gsqydnteg" path="res://Entities/Items/Resources/Processed/cloud_ball.tres" id="3_tf2lj"]

[sub_resource type="AtlasTexture" id="AtlasTexture_tf2lj"]
atlas = ExtResource("2_nyli1")
region = Rect2(64, 64, 32, 32)

[resource]
resource_name = "Cloud ball"
script = ExtResource("2_d7o1h")
processing_building = 9
input_resources = Dictionary[ExtResource("1_x8ws1"), int]({
ExtResource("2_tf2lj"): 1
})
output_resources = Dictionary[ExtResource("1_x8ws1"), int]({
ExtResource("3_tf2lj"): 2
})
processing_time = 5.0
unlocked_by_default = true
recipe_sprite = SubResource("AtlasTexture_tf2lj")
recipe_name = "Cloud ball"
metadata/_custom_type_script = "uid://kiuh738llx2t"

[gd_resource type="Resource" script_class="ItemRecipe" load_steps=7 format=3 uid="uid://vjsxl3t5lb6n"]

[ext_resource type="Script" uid="uid://dta70er7xbp1f" path="res://Entities/Items/item_data.gd" id="1_4g57m"]
[ext_resource type="Script" uid="uid://kiuh738llx2t" path="res://Entities/Items/item_recipe.gd" id="2_6v1py"]
[ext_resource type="Resource" uid="uid://cl1jgu5ftuhf4" path="res://Entities/Items/Resources/Ores/cobalt_ore.tres" id="2_e34uh"]
[ext_resource type="Resource" uid="uid://bpg38ienkqc4q" path="res://Entities/Items/Resources/Processed/cobalt_ingot.tres" id="3_iie4d"]
[ext_resource type="Texture2D" uid="uid://by0y44x5wge6r" path="res://assets/Sprites/32x32/SpriteSheets/Refined_items.png" id="4_jsxwo"]

[sub_resource type="AtlasTexture" id="AtlasTexture_esc0w"]
atlas = ExtResource("4_jsxwo")
region = Rect2(0, 32, 32, 32)

[resource]
resource_name = "Cobalt ingot"
script = ExtResource("2_6v1py")
processing_building = 8
input_resources = Dictionary[ExtResource("1_4g57m"), int]({
ExtResource("2_e34uh"): 2
})
output_resources = Dictionary[ExtResource("1_4g57m"), int]({
ExtResource("3_iie4d"): 1
})
processing_time = 0.0
unlocked_by_default = true
recipe_sprite = SubResource("AtlasTexture_esc0w")
recipe_name = "Cobalt ingot"
metadata/_custom_type_script = "uid://kiuh738llx2t"

[gd_resource type="Resource" script_class="ItemRecipe" load_steps=8 format=3 uid="uid://d07hmmji42lir"]

[ext_resource type="Script" uid="uid://dta70er7xbp1f" path="res://Entities/Items/item_data.gd" id="1_fkpdw"]
[ext_resource type="Resource" uid="uid://bws7gp7s3q8i6" path="res://Entities/Items/Resources/Crafted/copper_wire.tres" id="2_1r7ol"]
[ext_resource type="Script" uid="uid://kiuh738llx2t" path="res://Entities/Items/item_recipe.gd" id="2_8mlr5"]
[ext_resource type="Resource" uid="uid://b1ab8o4g5cyr4" path="res://Entities/Items/Resources/Processed/silicon.tres" id="3_gv7i1"]
[ext_resource type="Resource" uid="uid://cxch5jhgtuf81" path="res://Entities/Items/Resources/Crafted/circuit_board.tres" id="4_p4yh6"]
[ext_resource type="Texture2D" uid="uid://dnukrtectax3c" path="res://assets/Sprites/32x32/SpriteSheets/Advanced_items.png" id="5_gv7i1"]

[sub_resource type="AtlasTexture" id="AtlasTexture_p7pi1"]
atlas = ExtResource("5_gv7i1")
region = Rect2(64, 32, 32, 32)

[resource]
resource_name = "Circuit board"
script = ExtResource("2_8mlr5")
processing_building = 11
input_resources = Dictionary[ExtResource("1_fkpdw"), int]({
ExtResource("2_1r7ol"): 1,
ExtResource("3_gv7i1"): 1
})
output_resources = Dictionary[ExtResource("1_fkpdw"), int]({
ExtResource("4_p4yh6"): 1
})
processing_time = 10.0
unlocked_by_default = false
recipe_sprite = SubResource("AtlasTexture_p7pi1")
recipe_name = "Circuit board"
metadata/_custom_type_script = "uid://kiuh738llx2t"

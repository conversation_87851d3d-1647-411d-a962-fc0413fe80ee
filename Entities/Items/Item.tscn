[gd_scene load_steps=10 format=3 uid="uid://beio7ggfpkg4b"]

[ext_resource type="Script" uid="uid://pq572nt1he07" path="res://Entities/Items/item.gd" id="1_07n8b"]
[ext_resource type="Texture2D" uid="uid://djpoaqnsf67vf" path="res://assets/IconGodotNode/node_2D/icon_crate.png" id="3_8caw1"]
[ext_resource type="Shader" uid="uid://d248yduxgow4k" path="res://Shaders/ItemColor.gdshader" id="3_bts8b"]
[ext_resource type="Shader" uid="uid://sw128j8q52yo" path="res://Shaders/ItemHeatGlow.gdshader" id="3_pushi"]
[ext_resource type="Script" uid="uid://cnlbixiie8olb" path="res://Entities/Components/Debug/item_info_component.gd" id="5_bts8b"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_8caw1"]
resource_local_to_scene = true
shader = ExtResource("3_pushi")
shader_parameter/temperature = 0.0

[sub_resource type="Gradient" id="Gradient_8caw1"]
resource_local_to_scene = true
offsets = PackedFloat32Array(0, 0.00606061, 1)
colors = PackedColorArray(1, 1, 1, 1, 0.969863, 0.969863, 0.969863, 0.969863, 0, 0, 0, 0)

[sub_resource type="GradientTexture2D" id="GradientTexture2D_pushi"]
resource_local_to_scene = true
gradient = SubResource("Gradient_8caw1")
width = 32
height = 32
fill = 1
fill_from = Vector2(0.5, 0.5)
fill_to = Vector2(0.5, 1)
metadata/_snap_enabled = true

[sub_resource type="ShaderMaterial" id="ShaderMaterial_0njkw"]
resource_local_to_scene = true
shader = ExtResource("3_bts8b")
shader_parameter/temperature = 0.0

[node name="Item" type="Node2D" groups=["Item"]]
script = ExtResource("1_07n8b")

[node name="ItemHeatGlow" type="Sprite2D" parent="."]
z_index = 5
z_as_relative = false
material = SubResource("ShaderMaterial_8caw1")
texture = SubResource("GradientTexture2D_pushi")

[node name="ItemSprite" type="Sprite2D" parent="."]
z_index = 5
z_as_relative = false
material = SubResource("ShaderMaterial_0njkw")
texture = ExtResource("3_8caw1")

[node name="Item Info" type="Node2D" parent="."]
script = ExtResource("5_bts8b")
metadata/_custom_type_script = "uid://cnlbixiie8olb"

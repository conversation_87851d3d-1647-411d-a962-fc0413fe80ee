[gd_resource type="Resource" script_class="BuildingStats" load_steps=9 format=3 uid="uid://cdblcn24bddyf"]

[ext_resource type="Texture2D" uid="uid://d1wf6khmm66j4" path="res://assets/Sprites/HUD/Direction.png" id="1_5t8g4"]
[ext_resource type="Script" uid="uid://c7s4elarc1emd" path="res://Entities/Buildings/building base/building_stats.gd" id="1_bs85k"]
[ext_resource type="Script" uid="uid://b2m8c861chvcm" path="res://Entities/Buildings/pipes/pipe_building.gd" id="1_chs5y"]
[ext_resource type="Texture2D" uid="uid://dmmlkudi0sa6x" path="res://assets/Sprites/32x32/SpriteSheets/pipe-Sheet.png" id="2_gbqvv"]
[ext_resource type="PackedScene" uid="uid://dbtas82ndq74q" path="res://Entities/Buildings/pipes/pipe_directions.tscn" id="2_wfjbf"]
[ext_resource type="Script" uid="uid://dta70er7xbp1f" path="res://Entities/Items/item_data.gd" id="4_elf43"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_wfjbf"]

[sub_resource type="AtlasTexture" id="AtlasTexture_chs5y"]
atlas = ExtResource("2_gbqvv")
region = Rect2(0, 0, 32, 32)

[resource]
resource_name = "Pipe"
script = ExtResource("1_bs85k")
translation_key = ""
dimensions = Vector2i(1, 1)
is_rotatable = false
sync_animation = false
building_type = 0
components = Array[Resource]([ExtResource("2_wfjbf")])
cost = Dictionary[ExtResource("4_elf43"), int]({})
is_replaceable = true
should_free_after_replace = true
building_group = 1
building_subgroup = 0
player_interactable = true
texture = SubResource("AtlasTexture_chs5y")
collision_shape = SubResource("RectangleShape2D_wfjbf")
building_script = ExtResource("1_chs5y")
building_info_texture = ExtResource("1_5t8g4")
description = "Used to transport liquids. WIP might break the game."
unlocked_by_default = true
menu_order_priority = 0
menu = 1
heat_strength = 0
heat_radius = 0
metadata/_custom_type_script = "uid://c7s4elarc1emd"

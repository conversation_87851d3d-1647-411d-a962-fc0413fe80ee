[gd_scene load_steps=6 format=3 uid="uid://c5hbk2wlqmp0g"]

[ext_resource type="Script" uid="uid://c8u5m1xm4dfp" path="res://Entities/Buildings/Power Network/testing/testing_power_battery.gd" id="1_n41rf"]
[ext_resource type="Script" uid="uid://h5swltwpuw8s" path="res://Entities/Components/Power/power_buffer_component.gd" id="2_o3kjs"]
[ext_resource type="Script" uid="uid://cxc5br52a2krl" path="res://Entities/Components/Power/power_provider_component.gd" id="3_8sswq"]
[ext_resource type="Script" uid="uid://dfrsc76sgl6kk" path="res://Entities/Components/Power/power_receiver_component.gd" id="4_50a01"]
[ext_resource type="Script" uid="uid://2ud046riy2x2" path="res://Entities/Components/Debug/power_info_component.gd" id="5_runih"]

[node name="TestingPowerBattery" type="Node2D"]
script = ExtResource("1_n41rf")

[node name="PowerBufferComponent" type="Node2D" parent="."]
script = ExtResource("2_o3kjs")
metadata/_custom_type_script = "uid://h5swltwpuw8s"

[node name="PowerProviderComponent" type="Node2D" parent="." groups=["power_provider"]]
script = ExtResource("3_8sswq")
metadata/_custom_type_script = "uid://cxc5br52a2krl"

[node name="PowerReceiverComponent" type="Node2D" parent="." groups=["power_receiver"]]
script = ExtResource("4_50a01")
metadata/_custom_type_script = "uid://dfrsc76sgl6kk"

[node name="Power Info" type="Node2D" parent="."]
script = ExtResource("5_runih")
metadata/_custom_type_script = "uid://2ud046riy2x2"

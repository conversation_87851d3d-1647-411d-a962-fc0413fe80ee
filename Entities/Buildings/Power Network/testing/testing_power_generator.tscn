[gd_scene load_steps=4 format=3 uid="uid://c5dgr7sr43otq"]

[ext_resource type="Script" uid="uid://b4vsxe317c4pv" path="res://Entities/Buildings/Power Network/testing/testing_power_generator.gd" id="1_sr7wb"]
[ext_resource type="Script" uid="uid://cxc5br52a2krl" path="res://Entities/Components/Power/power_provider_component.gd" id="2_en51k"]
[ext_resource type="Script" uid="uid://2ud046riy2x2" path="res://Entities/Components/Debug/power_info_component.gd" id="3_e2pw0"]

[node name="TestingPowerGenerator" type="Node2D"]
script = ExtResource("1_sr7wb")

[node name="PowerProviderComponent" type="Node2D" parent="." groups=["power_provider"]]
script = ExtResource("2_en51k")
metadata/_custom_type_script = "uid://cxc5br52a2krl"

[node name="Power Info" type="Node2D" parent="."]
script = ExtResource("3_e2pw0")
metadata/_custom_type_script = "uid://2ud046riy2x2"

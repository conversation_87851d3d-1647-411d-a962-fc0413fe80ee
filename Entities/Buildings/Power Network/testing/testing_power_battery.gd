extends Node2D

@onready var power_buffer_component: PowerBufferComponent = $PowerBufferComponent
@onready var power_provider_component: PowerProviderComponent = $PowerProviderComponent
@onready var power_receiver_component: PowerReceiverComponent = $PowerReceiverComponent


func _ready() -> void:
	power_receiver_component.power_input_priority = PowerInputPriority.Enum.BATTERY
	power_provider_component.power_output_priority = PowerOutputPriority.Enum.BATTERY

	power_buffer_component.max_power_stored = 10000.0

	power_provider_component.power_rate = 100.0
	power_receiver_component.power_rate = 100.0

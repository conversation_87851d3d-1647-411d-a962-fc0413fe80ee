extends Node2D

@onready var power_receiver_component: PowerReceiverComponent = $PowerReceiverComponent
@onready var power_buffer_component: PowerBufferComponent = $PowerBufferComponent


func _ready() -> void:
	power_receiver_component.power_input_priority = PowerInputPriority.Enum.INTENSE


func _input(event: InputEvent) -> void:
	if event.is_action_released(&"camera_move_down"):
		power_buffer_component.stored_power -= 10

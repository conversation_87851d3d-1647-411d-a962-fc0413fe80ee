class_name ItemGenerator
extends ConveyorBasedBuilding

# TODO: the timer might be part of all item handling buildings
@onready var timer: Timer = $Timer

# TODO: rename to ItemStats
@onready var item_data: ItemData = load("res://Entities/Items/Resources/Crafted/copper_wire.tres")


func _ready() -> void:
	timer.wait_time = 0.5
	timer.timeout.connect(_on_timer_timeout)
	timer.start()


func _after_push_item(_item: Item) -> void:
	timer.start()


func _on_timer_timeout() -> void:
	_held_item = _instantiate_item(item_data)

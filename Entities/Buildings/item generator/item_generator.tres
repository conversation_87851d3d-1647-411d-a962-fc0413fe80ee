[gd_resource type="Resource" script_class="ItemHandlingBuildingStats" load_steps=8 format=3 uid="uid://bngmej0pvatss"]

[ext_resource type="Script" uid="uid://datmaph0rdfke" path="res://Entities/Buildings/item generator/item_generator.gd" id="1_1vifp"]
[ext_resource type="Texture2D" uid="uid://bx5p1ttcts88f" path="res://assets/Sprites/HUD/AllOutput.png" id="1_qye6k"]
[ext_resource type="Script" uid="uid://qrdti4s4su0t" path="res://Entities/Buildings/building base/conveyor_based_building_stats.gd" id="2_qye6k"]
[ext_resource type="Texture2D" uid="uid://qet3baxifsjq" path="res://assets/Sprites/32x32/SpriteSheets/Gen_sprite_sheet_anim.png" id="3_m2qtj"]
[ext_resource type="Script" uid="uid://dta70er7xbp1f" path="res://Entities/Items/item_data.gd" id="3_nmlvo"]
[ext_resource type="Resource" uid="uid://bcxlh5grgyr6r" path="res://Entities/Items/Resources/Processed/copper_ingot.tres" id="4_nmlvo"]

[sub_resource type="AtlasTexture" id="AtlasTexture_2r6bj"]
atlas = ExtResource("3_m2qtj")
region = Rect2(0, 0, 32, 32)

[resource]
resource_name = "Item Generator"
script = ExtResource("2_qye6k")
input_directions = 0
output_directions = 15
transport_speed = 0.0
translation_key = ""
dimensions = Vector2i(1, 1)
is_rotatable = false
sync_animation = false
building_type = 12
components = Array[Resource]([])
cost = Dictionary[ExtResource("3_nmlvo"), int]({
ExtResource("4_nmlvo"): 10
})
is_replaceable = true
should_free_after_replace = true
building_group = 0
building_subgroup = 2
player_interactable = true
texture = SubResource("AtlasTexture_2r6bj")
building_script = ExtResource("1_1vifp")
building_info_texture = ExtResource("1_qye6k")
description = "Generates infinite copper wire. Debug building."
unlocked_by_default = false
menu_order_priority = 0
menu = 0
heat_strength = 0
heat_radius = 0
metadata/_custom_type_script = "uid://qrdti4s4su0t"

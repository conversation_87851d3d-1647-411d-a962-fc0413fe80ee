[gd_resource type="Resource" script_class="ItemHandlingBuildingStats" load_steps=9 format=3 uid="uid://dt84gufsp5ag6"]

[ext_resource type="SpriteFrames" uid="uid://dmuauo4oj028m" path="res://assets/Sprites/Animations/32x32/Conveyor.tres" id="1_uv56o"]
[ext_resource type="Texture2D" uid="uid://d1wf6khmm66j4" path="res://assets/Sprites/HUD/Direction.png" id="2_dgrpd"]
[ext_resource type="Script" uid="uid://wc3xoylk0uc3" path="res://Entities/Buildings/conveyor belt/conveyor_belt.gd" id="2_xm3n8"]
[ext_resource type="Script" uid="uid://qrdti4s4su0t" path="res://Entities/Buildings/building base/conveyor_based_building_stats.gd" id="3_dgrpd"]
[ext_resource type="Script" uid="uid://dta70er7xbp1f" path="res://Entities/Items/item_data.gd" id="4_dgrpd"]
[ext_resource type="Texture2D" uid="uid://bnhcqxfsi32ec" path="res://assets/Sprites/32x32/SpriteSheets/Simpleconveyor_sprite_sheet_anim.png" id="4_qahpd"]
[ext_resource type="Resource" uid="uid://cn13mxfv6t6ri" path="res://Entities/Items/Resources/Processed/iron_ingot.tres" id="5_qahpd"]

[sub_resource type="AtlasTexture" id="AtlasTexture_q1wsc"]
atlas = ExtResource("4_qahpd")
region = Rect2(0, 64, 32, 32)

[resource]
resource_name = "Conveyor Belt"
script = ExtResource("3_dgrpd")
input_directions = 14
output_directions = 1
transport_speed = 0.0
translation_key = "CONVEYOR_BELT"
dimensions = Vector2i(1, 1)
is_rotatable = true
sync_animation = true
building_type = 1
components = Array[Resource]([])
cost = Dictionary[ExtResource("4_dgrpd"), int]({
ExtResource("5_qahpd"): 1
})
is_replaceable = true
should_free_after_replace = true
building_group = 1
building_subgroup = 0
player_interactable = true
texture = SubResource("AtlasTexture_q1wsc")
animation_frames = ExtResource("1_uv56o")
building_script = ExtResource("2_xm3n8")
building_info_texture = ExtResource("2_dgrpd")
description = "Most basic way of transporting items."
unlocked_by_default = true
menu_order_priority = -1
menu = 0
heat_strength = 0
heat_radius = 0
metadata/_custom_type_script = "uid://qrdti4s4su0t"

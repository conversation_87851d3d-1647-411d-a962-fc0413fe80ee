## Stats of a building
class_name BuildingStats
extends Resource

enum MenuType { BUILDING, PLUMBING, WIRING, NONE }

@export_category("Logic Related Data")
## Experimental key to the localization dictionary
@export var translation_key: String
## Dimensions of a building.
@export var dimensions: Vector2i = Vector2i.ONE
## Whether the building (namely, the sprite) is rotateable.
@export var is_rotatable: bool = false
## Whether the building has to sync its animation with other buildings of the same type.
## NOTE: This also starts animation on building placement
@export var sync_animation: bool = false
## The type of a building
@export var building_type: BuildingType.Enum
## Script of various components that are going to be added as nodes to the components of buildings.
@export var components: Array[Resource]
## List of resources needed to build the building
@export var cost: Dictionary[ItemData, int]
## Decides wheter the building can be replaced with the same building
@export var is_replaceable: bool = true
## Decides wheter the original build should be deleted when replacing
@export var should_free_after_replace: bool = true
## Used for sorting buildings
@export var building_group: BuildingGroup.Enum
## Used for sorting buildings
@export var building_subgroup: BuildingSubgroup.Enum
## Decides whether the player can build and demolsih the building
@export var player_interactable: bool = true

@export_category("Other Resources")
## Texture that should be bound to a sprite at runtime.[br]
## This value is [b]required[\b].
@export var texture: Texture2D
## Collistion shape that shall be bound at runtime.
@export var collision_shape: Shape2D
## Animation that shall be bound at runtime.
@export var animation_frames: SpriteFrames = null
## Script related to [class Building].[br]
## This value is [b]required[\b]
@export var building_script: Script = null
## Texture that is bound to the building info at runtime.[br]
## It might be set to null.
@export var building_info_texture: Texture2D = null

@export var building_delete_texture: Texture2D = null
## Description of what the building does
@export var description: String = "Description not defined"

@export_category("Unlock system")
@export var unlocked_by_default: bool = false

@export_category("HUD system")
## Higher value will be added to right side of bar
@export var menu_order_priority: int = 0
## Type of menu the building will show up in
@export var menu: MenuType

@export_category("Heat source")
@export var heat_strength: int = 0
@export var heat_radius: int = 0


func _ready() -> void:
	print("initializing the building resource")
	assert(texture != null, "Texture is required")

class_name Building
extends Node2D

## Emitted when the stats of a building did change.
signal stats_changed

# Why is it not a standalone component? Because such a component would need to have signals
# connected. This connection would have to be done in each _ready function and then each
# child class of Building would have to call _ready of its super class. This way we add more
# code here, but we do not have to think about _ready calling.
const DIMENSIONS_TO_HOVER_TEXTURE_MAP: Dictionary[Vector2i, CompressedTexture2D] = {
	Vector2i.ONE: preload("res://assets/Sprites/HUD/Selection.png"),
	Vector2i(2, 2): preload("res://assets/Sprites/HUD/Selection2x2.png"),
}

const INFO_BOX_PREFAB: PackedScene = preload("res://UI/building_select_info.tscn")

## Unreasonably large integer that denotes the maximal supported distance to other building.
const MAX_DISTANCE: int = 2 ** 16

@export_category("Prefab Data")
## The reference to the resource that is setting the stats of current building.
@export var stats: BuildingStats:
	set(new_stats):
		stats = new_stats
		stats_changed.emit()
	get:
		return stats

var heat_source_data: HeatSource = HeatSource.new()

## The sprite of the building that shall be used mainly for icons in the UI.
@onready var sprite: Sprite2D = $Sprite2D
## An animation of the building that is showing that the building is in a process.
@onready var animation: AnimatedSprite2D = $AnimatedSprite2D
## The shape of the building collision
@onready var collision_shape: CollisionShape2D = $"Physics Body/CollisionShape2D"
## The sprite of the building info. It should be shown when building is selected.
@onready var building_info_sprite: Sprite2D = $InfoSprite

@onready var area2d_collision_shape: CollisionShape2D = $Area2D/CollisionShape2D

@onready var building_delete_sprite: Sprite2D = $DeleteSprite

@onready var hover_sprite: Sprite2D = $HoverSprite

@export_category("Runtime Data")
@export var tiles_occupied_by_building: Array[Vector2i]
@export var components_node: Node2D


## Calculates and populates the tile coordinates occupied by this building.
func calculate_tiles_covered_by_building(origin: Vector2i) -> void:
	for x_offset in range(stats.dimensions.x):
		for y_offset in range(stats.dimensions.y):
			tiles_occupied_by_building.append(origin + Vector2i(x_offset, y_offset))


## Loads the building to the scene.[br]
## It should be used only by the save manager.
func load_to_scene() -> void:
	# bind current stats of the building. Otherwise the sprites do not have correct
	# values.
	bind_stats()

	# Update the stats of all tiems held by this building.
	if self is ItemHandlingBuilding:
		for item: Item in (self as ItemHandlingBuilding).get_output_items():
			if item != null:
				item.reload_stats()

	# Update the state of building mode manager.
	BuildingModeManager.set_tiles_as_occupied(self)
	BuildingModeManager._sync_animation(self)


func tile_distance_to(other: Building) -> int:
	var distances: Array[int]
	for coord: Vector2i in other.tiles_occupied_by_building:
		var distance: float = (tiles_occupied_by_building.front() as Vector2i).distance_to(coord)
		# in tile distance we care only about the whole tiles
		if is_equal_approx(distance, float(int(distance))):
			distances.append(int(distance))
	if distances.is_empty():
		return MAX_DISTANCE
	return distances.min()


## Binds currently assigned stats to child nodes.[br]
## If we bind new stats we have to manually change them.
func bind_stats() -> void:
	sprite.texture = stats.texture

	hover_sprite.texture = DIMENSIONS_TO_HOVER_TEXTURE_MAP.get(stats.dimensions, null)
	area2d_collision_shape.scale = stats.dimensions
	building_info_sprite.texture = stats.building_info_texture
	building_delete_sprite.texture = stats.building_delete_texture

	animation.sprite_frames = stats.animation_frames
	if animation.sprite_frames != null:
		sprite.hide()

	collision_shape.shape = stats.collision_shape


## Method that is called by the builder at the moment of building a Building.[br]
## It is supposed to be implemented by child classes.
func on_build() -> void:
	pass


func on_demolish() -> void:
	pass


func on_replace_build() -> void:
	pass


# TODO: this might not be needed
func connect_to_others() -> void:
	pass


# This has to be defined to connect it properly to some signals. At runtime the override defined
# by some child shall be used.
func _on_building_built(_building: Building, _is_replacing: bool) -> void:
	pass


func initialize_building_heat_source(heat_manager: HeatHandler) -> void:
	for component in components_node.get_children():
		LogManager.debug("component %s" % component.name, self)
		if component is HeatSource:
			var heat_component: HeatSource = component
			heat_component.radius = stats.heat_radius
			heat_component.strength = stats.heat_strength
			heat_component.tile_position = tiles_occupied_by_building[0]
			heat_component.heat_manager = heat_manager
			LogManager.debug("found heat source", self)
			heat_component.add_building_heat_source()
			break


var current_info_box: BuildingSelectInfo = null


## Arbitrary action that should be done when a building is selected.
func on_select_action() -> void:
	building_info_sprite.show()
	var info_box: BuildingSelectInfo = INFO_BOX_PREFAB.instantiate()
	get_tree().get_current_scene().find_child("UI").add_child(info_box)
	info_box.move_to_correct_location()
	info_box.building = self
	info_box.building_stats = stats
	current_info_box = info_box


## Arbitrary action that should be done when a building is deselected.
func on_deselect_action() -> void:
	building_info_sprite.hide()
	if current_info_box:
		current_info_box.queue_free()
		current_info_box = null


## Hides the destruction selection.
func hide_destruction_selection() -> void:
	hover_sprite.hide()
	# The default value of modulate is white.
	hover_sprite.modulate = Color.WHITE


## Shows the destruction selection.[br]
## Basically, it takes the hover sprite and modulates it to red.
func show_destruction_selection() -> void:
	hover_sprite.modulate = Color.RED
	hover_sprite.show()


func _on_ready() -> void:
	if stats == null:
		return
	bind_stats()


func _on_mouse_entered() -> void:
	if StateManager.state != StateManager.States.PLAY:
		return
	hover_sprite.show()


func _on_mouse_exited() -> void:
	if StateManager.state != StateManager.States.PLAY:
		return
	hover_sprite.hide()


func get_building_data() -> BuildingData:
	var building_data: BuildingData = BuildingData.new()
	# Note: position is the center of the building
	# 32 is the size of one tile
	building_data.position = position - (Vector2(stats.dimensions) - Vector2.ONE) * 32 / 2
	building_data.rotation_degrees = int(rotation_degrees)

	building_data.building_stats = stats

	building_data.component_data = get_components_data()
	return building_data


func get_components_data() -> Dictionary[Resource,Dictionary]:
	var data: Dictionary[Resource, Dictionary] = {}
	for component in components_node.get_children():
		if component is PowerBufferComponent:
			var script: Script = component.get_script()
			var comp_data: Dictionary = (component as PowerBufferComponent).get_data()
			if script:
				data[script] = comp_data
	return data


func initialize_components(data: Dictionary[Resource,Dictionary]) -> void:
	components_node = $Components
	# initialize all components
	for component in stats.components:
		var new_node: Node2D
		if component is Script:
			new_node = Node2D.new()
			new_node.set_script(component)
			new_node.name = (component as Script).get_global_name()
		elif component is PackedScene:
			new_node = (component as PackedScene).instantiate()
		else:
			push_error("Unknown type of resource encountered in the list of components.")
			continue

		components_node.add_child(new_node)

	set_components_data(data)


func set_components_data(data: Dictionary[Resource,Dictionary]) -> void:
	for component in components_node.get_children():
		if component is PowerBufferComponent:
			
			var script: Script = component.get_script()
			var component_data := data.get(script, {}) as Dictionary
			if not component_data.is_empty():
				(component as PowerBufferComponent).set_data(component_data)


func set_animation_progress(progress: float) -> void:
	if animation.sprite_frames == null:
		return

	var total_frames: int = animation.sprite_frames.get_frame_count(animation.animation)
	var target_frame: int = int(clampf(progress * float(total_frames - 1), 0.0, float(total_frames - 1)))
	animation.frame = target_frame

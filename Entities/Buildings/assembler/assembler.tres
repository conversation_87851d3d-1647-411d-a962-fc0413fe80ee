[gd_resource type="Resource" script_class="ItemHandlingBuildingStats" load_steps=14 format=3 uid="uid://cfyixy0lwgqpy"]

[ext_resource type="Script" uid="uid://qrdti4s4su0t" path="res://Entities/Buildings/building base/conveyor_based_building_stats.gd" id="1_fvqff"]
[ext_resource type="SpriteFrames" uid="uid://fw6adbajlg0s" path="res://assets/Sprites/Animations/64x64/Assembler.tres" id="1_if8wn"]
[ext_resource type="Script" uid="uid://cvwul46h3rvfx" path="res://Entities/Buildings/assembler/assembler.gd" id="2_0845m"]
[ext_resource type="Texture2D" uid="uid://d1rnqbhikdw60" path="res://assets/Sprites/HUD/Selection2x2.png" id="2_etr1y"]
[ext_resource type="Texture2D" uid="uid://7pcwr3stjf1" path="res://assets/Sprites/32x32/SpriteSheets/Assembler_sprite_sheet_anim.png" id="2_n20gt"]
[ext_resource type="Script" uid="uid://dfrsc76sgl6kk" path="res://Entities/Components/Power/power_receiver_component.gd" id="3_p3xxg"]
[ext_resource type="Script" uid="uid://2ud046riy2x2" path="res://Entities/Components/Debug/power_info_component.gd" id="4_etr1y"]
[ext_resource type="Script" uid="uid://h5swltwpuw8s" path="res://Entities/Components/Power/power_buffer_component.gd" id="6_d8elv"]
[ext_resource type="Script" uid="uid://dta70er7xbp1f" path="res://Entities/Items/item_data.gd" id="7_bjmhf"]
[ext_resource type="Resource" uid="uid://cn13mxfv6t6ri" path="res://Entities/Items/Resources/Processed/iron_ingot.tres" id="8_l3luu"]
[ext_resource type="Resource" uid="uid://bcxlh5grgyr6r" path="res://Entities/Items/Resources/Processed/copper_ingot.tres" id="9_0ddiw"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_if8wn"]

[sub_resource type="AtlasTexture" id="AtlasTexture_0845m"]
atlas = ExtResource("2_n20gt")
region = Rect2(0, 0, 64, 64)

[resource]
resource_name = "Assembler"
script = ExtResource("1_fvqff")
input_directions = 0
output_directions = 0
transport_speed = 0.0
translation_key = ""
dimensions = Vector2i(2, 2)
is_rotatable = false
sync_animation = false
building_type = 11
components = Array[Resource]([ExtResource("3_p3xxg"), ExtResource("4_etr1y"), ExtResource("6_d8elv")])
cost = Dictionary[ExtResource("7_bjmhf"), int]({
ExtResource("8_l3luu"): 3,
ExtResource("9_0ddiw"): 3
})
is_replaceable = false
should_free_after_replace = true
building_group = 2
building_subgroup = 0
player_interactable = true
texture = SubResource("AtlasTexture_0845m")
collision_shape = SubResource("RectangleShape2D_if8wn")
animation_frames = ExtResource("1_if8wn")
building_script = ExtResource("2_0845m")
building_info_texture = ExtResource("2_etr1y")
description = "Used for crafting more advanced resources. Requires inserters for input and output."
unlocked_by_default = true
menu_order_priority = 0
menu = 0
heat_strength = 0
heat_radius = 0
metadata/_custom_type_script = "uid://qrdti4s4su0t"

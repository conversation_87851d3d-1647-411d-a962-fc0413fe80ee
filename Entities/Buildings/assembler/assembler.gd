class_name Assembler
extends ItemHandlingBuilding

var _current_crafting_time: float = 0.0

var outputs: Array[Item]
var recipe: ItemRecipe

var _is_crafting: bool = false

# Array of items that are incomming but they are not in building yet
var _incoming_items: Array[Item]
# Dictionary of available resources, key: ItemData, value: int count
var _available_resources: Dictionary[ItemData, int]

@onready var power_receiver_component: PowerReceiverComponent = $Components/PowerReceiverComponent
@onready var power_buffer_component: PowerBufferComponent = $Components/PowerBufferComponent


func _ready() -> void:
	# TODO change this once recipe select exists
	var array: Array[ItemRecipe] = UnlockManager.get_complex_recipes(BuildingType.Enum.ASSEMBLER)
	if array.size() == 0:
		recipe = null
	else:
		recipe = array[0]

	power_buffer_component.init_from(power_receiver_component)


func get_held_items() -> Dictionary[ItemData, int]:
	var held_items: Dictionary[ItemData, int] = _available_resources.duplicate()
	#print("avail: ", _available_resources)
	#print("out: ", outputs)
	for item in outputs:
		if item.item_data not in held_items:
			held_items[item.item_data] = 1
		else:
			held_items[item.item_data] += 1
	#print(held_items)
	return held_items


func _on_push_item(item: Item, _building: Building) -> void:
	if item == null:
		return
	item_received.emit(item, self)


func _on_item_transport_confirm(item: Item, building: Building, _skip_animation: bool = false) -> void:
	if building == self:
		# if item.item_data.type not in _available_resources:
		# 	_available_resources[item.item_data] = 0
		# 	held_items_changed.emit(self)
		item.reparent(building)
		_incoming_items.append(item)


func _on_item_received(item: Item, building: Building) -> void:
	if item in outputs:
		item.global_position = (building as Inserter).arm_component.attach_point.global_position
		item_transport_confirmed.emit(item, building)
		outputs.erase(item)
		held_items_changed.emit(self)


func _is_item_acceptable(item: Item) -> bool:
	# Recipe is set using GUI
	if recipe == null:
		return false

	if outputs.size() > 0:
		return false

	var required: int = recipe.input_resources.get(item.item_data, -1)
	var available: int = _available_resources.get(item.item_data, 0)

	# Allow up to twice the required input (same as Assembler logic)
	return available < required * 2


func _after_process(delta: float) -> void:
	# Waits for animations to finish before deleting items
	var i: int = 0
	while i < _incoming_items.size():
		var item: Item = _incoming_items[i]
		if not item.is_moving():
			var item_count: int = _available_resources.get(item.item_data, 0)
			item_count += 1
			_available_resources.set(item.item_data, item_count)

			item.queue_free()
			_incoming_items.remove_at(i)
			held_items_changed.emit(self)
			continue
		else:
			i += 1

	if recipe == null:
		return

	if _is_crafting:
		if _current_crafting_time < recipe.processing_time:
			var work_done: float = power_buffer_component.work_based_on(power_receiver_component, delta)
			_current_crafting_time += work_done * delta
			processing_progress_changed.emit(_current_crafting_time / recipe.processing_time)
			set_animation_progress(_current_crafting_time / recipe.processing_time)
		else:
			_consume_resources()
			_generate_items()
			_is_crafting = false
			_current_crafting_time = 0.0
			set_animation_progress(0)
			processing_finished.emit()
			held_items_changed.emit(self)
		return

	for key in recipe.input_resources.keys() as Array[ItemData]:
		if _available_resources.get(key, -1) < recipe.input_resources[key]:
			return

	_is_crafting = true


func _generate_items() -> void:
	for item_data in recipe.output_resources.keys() as Array[ItemData]:
		for count in range(recipe.output_resources.get(item_data, 0)):
			var new_item: Item = _instantiate_item(item_data)
			outputs.push_back(new_item)


func _consume_resources() -> void:
	for item_data in recipe.input_resources.keys() as Array[ItemData]:
		_available_resources[item_data] -= recipe.input_resources[item_data]


func get_output_items() -> Array[Item]:
	return outputs


func _power_received(power: Variant, delta: Variant) -> void:
	if not _is_crafting:
		return

	# Not sure about this value it was copied from smeltery
	var provided_crafting: float = (power / power_receiver_component.power_rate) * delta
	_current_crafting_time += provided_crafting

	if recipe:
		processing_progress_changed.emit(_current_crafting_time / recipe.processing_time)
		set_animation_progress(_current_crafting_time / recipe.processing_time)


func _on_recipe_changed(new_recipe: Recipe) -> void:
	recipe = new_recipe

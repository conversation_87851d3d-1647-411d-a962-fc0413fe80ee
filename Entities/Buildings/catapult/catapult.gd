class_name Catapult
extends ConveyorBasedBuilding

@export var capacity: int = 5
@export var max_distance: int = 10
@export var max_air_time: float = 2.0
@export var shoot_delay: float = 0.5

var input_items: Array[Item] = []
var output_items: Array[Item] = []

var target_catapult: Catapult = null
var other_catapults: Dictionary[float, Catapult] = {}

var current_wait_time: float = 0.0

@onready var power_receiver_component: PowerReceiverComponent = $Components/PowerReceiverComponent
@onready var power_buffer_component: PowerBufferComponent = $Components/PowerBufferComponent

func _ready() -> void:
	power_buffer_component.init_from(power_receiver_component)


func get_output_items() -> Array[Item]:
	return output_items


func get_held_items() -> Dictionary[ItemData, int]:
	var held_items: Dictionary[ItemData, int] = {}
	
	for item: Item in input_items:
		if item.item_data not in held_items.keys():
			held_items[item.item_data] = 1
		else:
			held_items[item.item_data] += 1
	
	for item: Item in output_items:
		if item.item_data not in held_items.keys():
			held_items[item.item_data] = 1
		else:
			held_items[item.item_data] += 1
	
	return held_items


func _before_process(delta: float) -> void:
	# Process input item
	if _held_item != null:
		if len(input_items) < capacity:
			input_items.append(_held_item)
			held_items_changed.emit(self)
			_held_item = null

	# Check target catapult
	if target_catapult == null or target_catapult.output_items.size() > target_catapult.capacity:
		return

	# Check current item
	if input_items.size() == 0:
		return

	var current_item: Item = input_items.front()
	if current_item == null or current_item.is_moving():
		return

	var vector_to_other: Vector2i = (
		target_catapult.tiles_occupied_by_building.front() - tiles_occupied_by_building.front()
	)

	# Check wait time
	if current_wait_time < shoot_delay:
		current_wait_time += delta
		return

	# Check power
	var length_scale: float = vector_to_other.length() / max_distance
	var power_cost_scale: float = (1.0 + length_scale) / 2.0
	if not power_buffer_component.can_work_once(power_receiver_component, power_cost_scale):
		return

	# Shoot item
	power_buffer_component.work_once(power_receiver_component, power_cost_scale)
	current_wait_time -= shoot_delay

	var air_time: float = max_air_time * length_scale

	# Callback after landing process all stuff related to item transfer
	# This variable is to capture state of target_catapult when this labda is created
	var item_target_catapult := target_catapult
	current_item.movement_finished.connect(
		func() -> void:
			# Handeling of item landing
			current_item.item_sprite.z_index = 5

			if item_target_catapult == null:
				# Target is destroyed
				# TODO: some particles
				current_item.queue_free()
			else:
				# Target is still valid
				current_item.reparent(item_target_catapult),
		CONNECT_ONE_SHOT
	)

	var move_tween: Tween = current_item.create_tween()
	move_tween.set_ease(Tween.EASE_OUT_IN)
	current_item.move_to(target_catapult.global_position, air_time, false, move_tween)
	current_item.item_sprite.z_index = 10

	var scale_tween: Tween = current_item.create_tween()
	# Make them bigger and then return to their original size
	scale_tween.set_ease(Tween.EASE_IN)
	scale_tween.tween_property(current_item, "scale", current_item.scale * 1.5, air_time / 2.0)
	scale_tween.set_ease(Tween.EASE_OUT)
	scale_tween.tween_property(current_item, "scale", current_item.scale, air_time / 2.0)

	current_item.reparent(get_tree().current_scene)

	input_items.erase(current_item)
	target_catapult.output_items.append(current_item)
	held_items_changed.emit(self)
	target_catapult.held_items_changed.emit(target_catapult)

	(AudioManager as AudioManagerScript).create_audio(SoundEffect.SOUND_EFFECT_TYPE.SHOOT)


func _handle_catapult_linking(other_building: Building, _is_replacing: bool) -> bool:
	if other_building == self:
		return false

	var other_catapult := other_building as Catapult
	if other_catapult == null:
		return false

	var vector_to_other: Vector2i = (
		other_catapult.tiles_occupied_by_building.front() - tiles_occupied_by_building.front()
	)

	# If its too far ignore
	if vector_to_other.length() > max_distance:
		return false

	var front_vector: Vector2i = vector_from_direction_per_rotation_degrees(IODirection.Enum.FRONT, rotation_degrees)

	# Checks if other catapult is in front of this catapult
	if not (vector_to_other.x * front_vector.x + vector_to_other.y * front_vector.y) > 0:
		return false

	# Check if other catapult can be reached by straight line
	if vector_to_other.x * front_vector.y - vector_to_other.y * front_vector.x != 0:
		return false

	var other_catapult_back_vector: Vector2i = vector_from_direction_per_rotation_degrees(
		IODirection.Enum.BACK, other_catapult.rotation_degrees
	)

	# If they are not facing each other ignore
	if front_vector != other_catapult_back_vector:
		return false

	# Add link between catapults
	other_catapults[vector_to_other.length()] = other_catapult
	other_catapult.other_catapults[vector_to_other.length()] = self

	# Recalculate closes catapults
	set_closest_target()
	other_catapult.set_closest_target()

	return true


func set_closest_target() -> void:
	var min_distance: float = -1.0

	if other_catapults.size() > 0:
		min_distance = other_catapults.keys().min()

	if min_distance != -1.0:
		target_catapult = other_catapults[min_distance]


func _on_building_built(other_building: Building, _is_replacing: bool) -> void:
	_handle_catapult_linking(other_building, _is_replacing)
	super._on_building_built(other_building, _is_replacing)


func _on_item_received(item: Item, building: Building) -> void:
	if item in output_items:
		item_transport_confirmed.emit(item, building)
		output_items.erase(item)
		held_items_changed.emit(self)


func on_demolish() -> void:
	super.on_demolish()

	# Force all catapults that ware targeting this catapult to find new target instead
	for catapult: Catapult in other_catapults.values():
		# Remove self from other catapult targets
		var self_dist: float = catapult.other_catapults.find_key(self)
		catapult.other_catapults.erase(self_dist)

		# If self was target relink other catapult
		if catapult.target_catapult == self:
			catapult.set_closest_target()

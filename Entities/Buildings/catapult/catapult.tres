[gd_resource type="Resource" script_class="ItemHandlingBuildingStats" load_steps=11 format=3 uid="uid://dpgt8hvbaqaic"]

[ext_resource type="Script" uid="uid://qrdti4s4su0t" path="res://Entities/Buildings/building base/conveyor_based_building_stats.gd" id="1_1mokk"]
[ext_resource type="Texture2D" uid="uid://bwva5p7wl7alw" path="res://assets/Sprites/HUD/catapult_info.png" id="1_7g1gf"]
[ext_resource type="Script" uid="uid://ctylkc0l6ayxj" path="res://Entities/Buildings/catapult/catapult.gd" id="2_dbpju"]
[ext_resource type="Texture2D" uid="uid://bflrx8ws1u4rv" path="res://assets/Sprites/32x32/catapult.png" id="3_dbpju"]
[ext_resource type="Script" uid="uid://dfrsc76sgl6kk" path="res://Entities/Components/Power/power_receiver_component.gd" id="3_jfkpy"]
[ext_resource type="Script" uid="uid://2ud046riy2x2" path="res://Entities/Components/Debug/power_info_component.gd" id="4_7g1gf"]
[ext_resource type="Script" uid="uid://h5swltwpuw8s" path="res://Entities/Components/Power/power_buffer_component.gd" id="5_7g1gf"]
[ext_resource type="Script" uid="uid://dta70er7xbp1f" path="res://Entities/Items/item_data.gd" id="6_8rtag"]
[ext_resource type="Resource" uid="uid://bcxlh5grgyr6r" path="res://Entities/Items/Resources/Processed/copper_ingot.tres" id="7_2ts7p"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_xoj2d"]

[resource]
resource_name = "Catapult"
script = ExtResource("1_1mokk")
input_directions = 15
output_directions = 15
transport_speed = 0.0
translation_key = ""
dimensions = Vector2i(1, 1)
is_rotatable = true
sync_animation = false
building_type = 13
components = Array[Resource]([ExtResource("4_7g1gf"), ExtResource("3_jfkpy"), ExtResource("5_7g1gf")])
cost = Dictionary[ExtResource("6_8rtag"), int]({
ExtResource("7_2ts7p"): 2
})
is_replaceable = true
should_free_after_replace = true
building_group = 1
building_subgroup = 1
player_interactable = true
texture = ExtResource("3_dbpju")
collision_shape = SubResource("RectangleShape2D_xoj2d")
building_script = ExtResource("2_dbpju")
building_info_texture = ExtResource("1_7g1gf")
description = "Used for transporting items over a distance. Requires another catapult withing range that faces the oposite direction."
unlocked_by_default = false
menu_order_priority = 0
menu = 0
heat_strength = 0
heat_radius = 0
metadata/_custom_type_script = "uid://qrdti4s4su0t"

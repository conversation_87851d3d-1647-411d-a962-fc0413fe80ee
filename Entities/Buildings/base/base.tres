[gd_resource type="Resource" script_class="BuildingStats" load_steps=7 format=3 uid="uid://4n3lekspwh6s"]

[ext_resource type="Texture2D" uid="uid://b4lh516sopcqn" path="res://assets/Sprites/HUD/HQBuildInfo.png" id="1_grk87"]
[ext_resource type="Script" uid="uid://c7s4elarc1emd" path="res://Entities/Buildings/building base/building_stats.gd" id="1_wmvf8"]
[ext_resource type="Script" uid="uid://dq8mvpn5q5uq2" path="res://Entities/Buildings/base/base.gd" id="1_ywf6r"]
[ext_resource type="Script" uid="uid://dta70er7xbp1f" path="res://Entities/Items/item_data.gd" id="3_e1s4k"]
[ext_resource type="Texture2D" uid="uid://bejhjcswikyt1" path="res://assets/Sprites/32x32/ShipPossibly.png" id="3_grk87"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_e1s4k"]

[resource]
resource_name = "Base"
script = ExtResource("1_wmvf8")
translation_key = ""
dimensions = Vector2i(4, 2)
is_rotatable = false
sync_animation = false
building_type = 0
components = Array[Resource]([])
cost = Dictionary[ExtResource("3_e1s4k"), int]({})
is_replaceable = true
should_free_after_replace = true
building_group = 0
building_subgroup = 0
player_interactable = false
texture = ExtResource("3_grk87")
collision_shape = SubResource("RectangleShape2D_e1s4k")
building_script = ExtResource("1_ywf6r")
building_info_texture = ExtResource("1_grk87")
description = "Description not defined"
unlocked_by_default = true
menu_order_priority = 0
menu = 0
heat_strength = 0
heat_radius = 0
metadata/_custom_type_script = "uid://c7s4elarc1emd"

class_name In<PERSON><PERSON>
extends ItemHandlingBuilding

# NOTE: Currently, the inserter is blocking, as it reserves its space in the output
# building, it makes sense for crafting and production buildings, but it does not behave
# so good with conveyor belts. We have to come up with some more clever approach.

var _held_item: Item = null

# Track if the inserter is waiting for power to move
var _pending_move: bool = false

@onready var arm_component: InserterArmComponent = $Components/InserterArmComponent
@onready var power_receiver_component: PowerReceiverComponent = $Components/PowerReceiverComponent
@onready var power_buffer_component: PowerBufferComponent = $Components/PowerBufferComponent


func get_output_items() -> Array[Item]:
	if _held_item:
		return [_held_item]
	return []


func get_held_items() -> Dictionary[ItemData, int]:
	if _held_item:
		return { _held_item.item_data : 1}
	return {}


func _ready() -> void:
	arm_component.move_finished.connect(_on_arm_move_finished)
	arm_component.movement_started.connect(
		func() -> void:
			# Prevents arm from moving if no power is available at the start of movement
			arm_component.tween.set_speed_scale(0.0)
	)

	# Calculate power rate based on arm transition time
	var base_cost: int = 5
	var additional_cost_multiplier: float = 1.2

	# Modifies additional cost
	var power_multiplier: float = additional_cost_multiplier / arm_component.transition_time_multiplier
	# Removes UNMODIFIED additional cost and adds one to make valid it multiplier
	power_multiplier += 1.0 - additional_cost_multiplier

	power_multiplier *= arm_component.length
	var power_per_move := base_cost * power_multiplier

	power_receiver_component.power_rate = power_per_move
	power_buffer_component.init_from(power_receiver_component)


func _physics_process(delta: float) -> void:
	super._physics_process(delta)

	if _pending_move:
		_pending_move = false
		arm_component.switch_sides()

	if arm_component.is_moving:
		var work_done: float = power_buffer_component.work_based_on(power_receiver_component, delta)
		arm_component.tween.set_speed_scale(work_done)


func _on_item_received(item: Item, building: Building) -> void:
	if item == _held_item:
		item_transport_confirmed.emit(_held_item, building, true)
		_held_item = null
		held_items_changed.emit(self)
		if item.movement_finished.is_connected(arm_component.switch_sides):
			item.movement_finished.disconnect(arm_component.switch_sides)

		# Move to input position
		_pending_move = true


## Checks whether other building is input of this inserter.
func _is_input(building: ItemHandlingBuilding) -> bool:
	# Building must exists
	if building == null:
		return false
	# inserter arm must be in correct position
	if arm_component.arm_positions.input not in building.tiles_occupied_by_building:
		return false
	# Building has to be able to hold items
	if building is not ItemHandlingBuilding:
		return false
	return true


## Checks whether other building is output of this inserter.
func _is_output(building: ItemHandlingBuilding) -> bool:
	if building == null:
		return false
	if arm_component.arm_positions.output not in building.tiles_occupied_by_building:
		return false
	if building is not ItemHandlingBuilding:
		return false
	return true


## Connects other building as an input to this inserter.
func _connect_as_input(building: ItemHandlingBuilding) -> void:
	building.has_item.connect(_on_has_item)
	item_received.connect(building._on_item_received)
	building.item_transport_confirmed.connect(_on_item_transport_confirm)
	input_buildings.append(building)
	building.output_buildings.append(self)


## Connects other buildign as an output to this inserter
func _connect_as_output(building: ItemHandlingBuilding) -> void:
	push_item.connect(building._on_push_item)
	building.item_received.connect(_on_item_received)
	item_transport_confirmed.connect(building._on_item_transport_confirm)
	output_buildings.append(building)
	building.input_buildings.append(self)


# Due to the fact, that other buildings do not have to know that an inserter is attached, and
# also because I don't want to further complicate the conveyor belt building method the inserter
# is able to connect to other buildings on build.
func on_build() -> void:
	arm_component.initialize_arm(tiles_occupied_by_building.front() as Vector2i, rotation_degrees)

	var input_building: ItemHandlingBuilding = BuildingModeManager.occupied_tiles.get(
		arm_component.arm_positions.input, null
	)
	if _is_input(input_building):
		_connect_as_input(input_building)

	var output_building: ItemHandlingBuilding = BuildingModeManager.occupied_tiles.get(
		arm_component.arm_positions.output, null
	)
	if _is_output(output_building):
		_connect_as_output(output_building)


func _on_building_built(other_building: Building, _is_replacing: bool) -> void:
	var other_item_handling_building := other_building as ItemHandlingBuilding
	if other_item_handling_building == null:
		return

	# The building is too far away to reach with our arm
	if tile_distance_to(other_item_handling_building) > int(arm_component.length):
		return

	if _is_input(other_item_handling_building):
		_connect_as_input(other_item_handling_building)
	elif _is_output(other_item_handling_building):
		_connect_as_output(other_item_handling_building)


func _is_item_pushable(item: Item) -> bool:
	if arm_component.is_in_input_position:
		return false
	if arm_component.is_moving:
		return false
	if not super._is_item_pushable(item):
		return false
	return true


func _on_has_item(item: Item, _building: Building) -> void:
	if not arm_component.is_in_input_position:
		return

	if arm_component.is_moving:
		return

	if _held_item != null:
		return

	if output_buildings.is_empty():
		return

	var output_building: ItemHandlingBuilding = output_buildings.front()

	# if the building is in the process of deletion
	if output_building == null:
		output_buildings.remove_at(0)
		return

	# If the output building is occupied, we cannot push the item
	if output_building.is_occupied:
		return

	# If the output building cannot accept the item, we cannot push it
	if output_building._is_item_acceptable(item) == false:
		return

	# Reserve our space in the output building
	var target_building: ItemHandlingBuilding = output_buildings.front()
	target_building.reserve(self)
	item_received.emit(item, self)


func _on_item_transport_confirm(item: Item, building: Building, _skip_animation: bool = false) -> void:
	if building == self:
		_held_item = item
		held_items_changed.emit(self)
		if item.is_moving():
			item.movement_finished.connect(_on_item_movement_finished, CONNECT_ONE_SHOT)
		else:
			_on_item_movement_finished()
	else:
		if output_buildings.is_empty():
			return
		if output_buildings.front() != null:
			(output_buildings.front() as ItemHandlingBuilding).un_reserve(self)


# Helper for when item movement finishes
func _on_item_movement_finished() -> void:
	_held_item.reparent(arm_component.attach_point)
	_held_item.move_to(arm_component.attach_point.global_position)

	# Move to output position
	_pending_move = true


func _on_arm_move_finished() -> void:
	push_item.emit(_held_item, self)

	if output_buildings.front() != null:
		(output_buildings.front() as ItemHandlingBuilding).un_reserve(self)


func on_demolish() -> void:
	if output_buildings.front() != null:
		(output_buildings.front() as ItemHandlingBuilding).un_reserve(self)

	super.on_demolish()

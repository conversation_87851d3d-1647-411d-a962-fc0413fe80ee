[gd_resource type="Resource" script_class="ItemHandlingBuildingStats" load_steps=12 format=3 uid="uid://dg43rxhtrt7vk"]

[ext_resource type="Script" uid="uid://cbjv6814ay2id" path="res://Entities/Buildings/Inserter/inserter.gd" id="1_4xrgg"]
[ext_resource type="Texture2D" uid="uid://bwa1e0o338agw" path="res://assets/Sprites/HUD/InserterLongBuildInfo.png" id="1_ecthy"]
[ext_resource type="PackedScene" uid="uid://irq4jaebk5sf" path="res://Entities/Buildings/Inserter/inserter_long_arm_component.tscn" id="2_4xrgg"]
[ext_resource type="Script" uid="uid://qrdti4s4su0t" path="res://Entities/Buildings/building base/conveyor_based_building_stats.gd" id="3_1mehg"]
[ext_resource type="Script" uid="uid://dfrsc76sgl6kk" path="res://Entities/Components/Power/power_receiver_component.gd" id="4_1mehg"]
[ext_resource type="Texture2D" uid="uid://b0xl8q07npqgm" path="res://assets/Sprites/32x32/InserterLongBase.png" id="4_4xrgg"]
[ext_resource type="Script" uid="uid://2ud046riy2x2" path="res://Entities/Components/Debug/power_info_component.gd" id="5_bbumu"]
[ext_resource type="Script" uid="uid://h5swltwpuw8s" path="res://Entities/Components/Power/power_buffer_component.gd" id="6_sfs3j"]
[ext_resource type="Script" uid="uid://dta70er7xbp1f" path="res://Entities/Items/item_data.gd" id="7_5fb8m"]
[ext_resource type="Resource" uid="uid://cn13mxfv6t6ri" path="res://Entities/Items/Resources/Processed/iron_ingot.tres" id="8_ux8rb"]
[ext_resource type="Resource" uid="uid://bcxlh5grgyr6r" path="res://Entities/Items/Resources/Processed/copper_ingot.tres" id="9_ebhx2"]

[resource]
resource_name = "Long Inserter"
script = ExtResource("3_1mehg")
input_directions = 3
output_directions = 3
transport_speed = 0.0
translation_key = "INSERTER_LONG"
dimensions = Vector2i(1, 1)
is_rotatable = true
sync_animation = false
building_type = 0
components = Array[Resource]([ExtResource("2_4xrgg"), ExtResource("4_1mehg"), ExtResource("5_bbumu"), ExtResource("6_sfs3j")])
cost = Dictionary[ExtResource("7_5fb8m"), int]({
ExtResource("8_ux8rb"): 2,
ExtResource("9_ebhx2"): 1
})
is_replaceable = true
should_free_after_replace = true
building_group = 1
building_subgroup = 1
player_interactable = true
texture = ExtResource("4_4xrgg")
building_script = ExtResource("1_4xrgg")
building_info_texture = ExtResource("1_ecthy")
description = "Takes more power than regular inserter but reaches twice as far."
unlocked_by_default = true
menu_order_priority = 0
menu = 0
heat_strength = 0
heat_radius = 0
metadata/_custom_type_script = "uid://qrdti4s4su0t"

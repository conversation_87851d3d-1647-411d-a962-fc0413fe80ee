[gd_resource type="Resource" script_class="ItemHandlingBuildingStats" load_steps=12 format=3 uid="uid://e56ikko2lqo5"]

[ext_resource type="Texture2D" uid="uid://clvqj7aljlec1" path="res://assets/Sprites/HUD/InserterBuildInfo.png" id="1_0vu3e"]
[ext_resource type="Script" uid="uid://cbjv6814ay2id" path="res://Entities/Buildings/Inserter/inserter.gd" id="1_7iqdt"]
[ext_resource type="PackedScene" uid="uid://f30l7vrk51we" path="res://Entities/Buildings/Inserter/inserter_fast_arm_component.tscn" id="2_7iqdt"]
[ext_resource type="Script" uid="uid://qrdti4s4su0t" path="res://Entities/Buildings/building base/conveyor_based_building_stats.gd" id="3_7silv"]
[ext_resource type="Texture2D" uid="uid://bb03a2cshh41w" path="res://assets/Sprites/32x32/InserterFastBase.png" id="4_7iqdt"]
[ext_resource type="Script" uid="uid://dfrsc76sgl6kk" path="res://Entities/Components/Power/power_receiver_component.gd" id="4_7silv"]
[ext_resource type="Script" uid="uid://2ud046riy2x2" path="res://Entities/Components/Debug/power_info_component.gd" id="5_6rl3t"]
[ext_resource type="Script" uid="uid://h5swltwpuw8s" path="res://Entities/Components/Power/power_buffer_component.gd" id="6_bybic"]
[ext_resource type="Script" uid="uid://dta70er7xbp1f" path="res://Entities/Items/item_data.gd" id="7_j8rls"]
[ext_resource type="Resource" uid="uid://cn13mxfv6t6ri" path="res://Entities/Items/Resources/Processed/iron_ingot.tres" id="8_fen3n"]
[ext_resource type="Resource" uid="uid://bcxlh5grgyr6r" path="res://Entities/Items/Resources/Processed/copper_ingot.tres" id="9_oli7y"]

[resource]
resource_name = "Fast Inserter"
script = ExtResource("3_7silv")
input_directions = 3
output_directions = 3
transport_speed = 0.0
translation_key = "INSERTER_FAST"
dimensions = Vector2i(1, 1)
is_rotatable = true
sync_animation = false
building_type = 0
components = Array[Resource]([ExtResource("2_7iqdt"), ExtResource("4_7silv"), ExtResource("5_6rl3t"), ExtResource("6_bybic")])
cost = Dictionary[ExtResource("7_j8rls"), int]({
ExtResource("8_fen3n"): 1,
ExtResource("9_oli7y"): 3
})
is_replaceable = true
should_free_after_replace = true
building_group = 1
building_subgroup = 1
player_interactable = true
texture = ExtResource("4_7iqdt")
building_script = ExtResource("1_7iqdt")
building_info_texture = ExtResource("1_0vu3e")
description = "Takes more power than the normal inserter but works faster."
unlocked_by_default = false
menu_order_priority = 0
menu = 0
heat_strength = 0
heat_radius = 0
metadata/_custom_type_script = "uid://qrdti4s4su0t"

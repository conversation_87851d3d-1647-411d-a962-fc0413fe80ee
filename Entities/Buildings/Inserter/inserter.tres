[gd_resource type="Resource" script_class="ItemHandlingBuildingStats" load_steps=12 format=3 uid="uid://djfskeupp2xap"]

[ext_resource type="Script" uid="uid://qrdti4s4su0t" path="res://Entities/Buildings/building base/conveyor_based_building_stats.gd" id="1_eo5km"]
[ext_resource type="Texture2D" uid="uid://clvqj7aljlec1" path="res://assets/Sprites/HUD/InserterBuildInfo.png" id="1_p37da"]
[ext_resource type="Script" uid="uid://cbjv6814ay2id" path="res://Entities/Buildings/Inserter/inserter.gd" id="1_qghj2"]
[ext_resource type="PackedScene" uid="uid://t3qyyt8sh0yk" path="res://Entities/Buildings/Inserter/inserter_arm_component.tscn" id="1_sy8o3"]
[ext_resource type="Texture2D" uid="uid://brro0svjm78l2" path="res://assets/Sprites/32x32/InserterBase.png" id="4_7gx43"]
[ext_resource type="Script" uid="uid://dfrsc76sgl6kk" path="res://Entities/Components/Power/power_receiver_component.gd" id="4_8spse"]
[ext_resource type="Script" uid="uid://2ud046riy2x2" path="res://Entities/Components/Debug/power_info_component.gd" id="5_ot4as"]
[ext_resource type="Script" uid="uid://h5swltwpuw8s" path="res://Entities/Components/Power/power_buffer_component.gd" id="6_dj54m"]
[ext_resource type="Script" uid="uid://dta70er7xbp1f" path="res://Entities/Items/item_data.gd" id="7_4opof"]
[ext_resource type="Resource" uid="uid://cn13mxfv6t6ri" path="res://Entities/Items/Resources/Processed/iron_ingot.tres" id="8_881vp"]
[ext_resource type="Resource" uid="uid://bcxlh5grgyr6r" path="res://Entities/Items/Resources/Processed/copper_ingot.tres" id="9_yvasv"]

[resource]
resource_name = "Inserter"
script = ExtResource("1_eo5km")
input_directions = 3
output_directions = 3
transport_speed = 0.0
translation_key = "INSERTER"
dimensions = Vector2i(1, 1)
is_rotatable = true
sync_animation = false
building_type = 14
components = Array[Resource]([ExtResource("1_sy8o3"), ExtResource("4_8spse"), ExtResource("5_ot4as"), ExtResource("6_dj54m")])
cost = Dictionary[ExtResource("7_4opof"), int]({
ExtResource("8_881vp"): 1,
ExtResource("9_yvasv"): 1
})
is_replaceable = true
should_free_after_replace = true
building_group = 1
building_subgroup = 0
player_interactable = true
texture = ExtResource("4_7gx43")
building_script = ExtResource("1_qghj2")
building_info_texture = ExtResource("1_p37da")
description = "At the cost of power takes items from the front inventory and puts them in the inventory of the building behind it."
unlocked_by_default = true
menu_order_priority = 0
menu = 0
heat_strength = 0
heat_radius = 0
metadata/_custom_type_script = "uid://qrdti4s4su0t"

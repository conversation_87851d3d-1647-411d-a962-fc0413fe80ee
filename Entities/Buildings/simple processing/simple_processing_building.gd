class_name SimpleProcessingBuilding
extends ConveyorBasedBuilding

var recipe: ItemRecipe = null
var outputs: Array[Item]

var _current_crafting_time: float = 0.0
var _is_crafting: bool = false

# Array of items incoming but not yet processed
var _incoming_items: Array[Item] = []
# Dictionary of available resources, key: ItemData, value: int count
var _available_resources: Dictionary[ItemData, int] = {}

@onready var power_receiver_component: PowerReceiverComponent = $Components/PowerReceiverComponent
@onready var power_buffer_component: PowerBufferComponent = $Components/PowerBufferComponent


func _ready() -> void:
	power_buffer_component.init_from(power_receiver_component)


func get_held_items() -> Dictionary[ItemData, int]:
	var held_items: Dictionary[ItemData, int] = _available_resources.duplicate()
	for item in outputs:
		if item.item_data not in held_items:
			held_items[item.item_data] = 1
		else:
			held_items[item.item_data] += 1
	return held_items


func _on_push_item(item: Item, _sender: Building) -> void:
	if item == null:
		return
	if not _is_item_acceptable(item):
		return
	item_received.emit(item, self)


func _on_item_transport_confirm(item: Item, building: Building, skip_animation: bool = false) -> void:
	if building == self:
		super._on_item_transport_confirm(item, building, skip_animation)
		_incoming_items.append(item)


func _on_item_received(item: Item, building: Building) -> void:
	if item in outputs:
		item_transport_confirmed.emit(item, building)
		outputs.erase(item)
		held_items_changed.emit(self)


func _is_item_acceptable(item: Item) -> bool:
	if outputs.size() > 0:
		return false
	
	if recipe == null:
		# Try to get recipe based on this item
		var res_recipe := UnlockManager.get_simple_recipe(stats.building_type, item.item_data)
		if res_recipe != null:
			recipe = res_recipe
			recipe_changed.emit(recipe)
		else:
			return false

	# Check if item type is needed and not fulfilled enough yet
	var required: int = recipe.input_resources.get(item.item_data, -1)
	var available: int = _available_resources.get(item.item_data, 0)

	# Allow up to twice the required input (same as Assembler logic)
	return available < required * 2


func _before_process(delta: float) -> void:
	# Process items not moving yet (animation finished)
	var i: int = 0
	while i < _incoming_items.size():
		var item: Item = _incoming_items[i]
		if not item.is_moving():
			var count: int = _available_resources.get(item.item_data, 0)
			count += 1
			_available_resources[item.item_data] = count
			item.queue_free()
			_incoming_items.remove_at(i)
			held_items_changed.emit(self)
			continue
		else:
			i += 1

	if recipe == null:
		return

	if _is_crafting:
		if _current_crafting_time < recipe.processing_time:
			var work_done: float = power_buffer_component.work_based_on(power_receiver_component, delta)
			_current_crafting_time += work_done * delta
			processing_progress_changed.emit(_current_crafting_time / recipe.processing_time)
			set_animation_progress(_current_crafting_time / recipe.processing_time)
		else:
			_consume_resources()
			_generate_items()
			_is_crafting = false
			_current_crafting_time = 0.0
			set_animation_progress(0)
			processing_finished.emit()
			held_items_changed.emit(self)
		return

	# Check if have enough resources to start crafting
	for resource in recipe.input_resources.keys() as Array[ItemData]:
		if _available_resources.get(resource, 0) < recipe.input_resources[resource]:
			return
	
	_is_crafting = true

func _generate_items() -> void:
	for item_data in recipe.output_resources.keys() as Array[ItemData]:
		for count in range(recipe.output_resources.get(item_data, 0)):
			var new_item: Item = _instantiate_item(item_data)
			outputs.push_back(new_item)

func _consume_resources() -> void:
	for resource in recipe.input_resources.keys() as Array[ItemData]:
		_available_resources[resource] -= recipe.input_resources[resource]

func get_output_items() -> Array[Item]:
	return outputs

func reset() -> void:
	recipe = null
	_current_crafting_time = 0.0
	_is_crafting = false
	_available_resources.clear()
	_incoming_items.clear()
	set_animation_progress(0)


func _power_received(power: float, delta: float) -> void:
	if power == 0:
		return

	# Does not have item but recieved some power
	assert(_held_item)

	var work_done: float = (power / power_receiver_component.power_rate) * delta
	_current_crafting_time += work_done

	if recipe:
		processing_progress_changed.emit(_current_crafting_time / recipe.processing_time)
		set_animation_progress(_current_crafting_time / recipe.processing_time)

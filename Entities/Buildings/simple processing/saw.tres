[gd_resource type="Resource" script_class="ItemHandlingBuildingStats" load_steps=13 format=3 uid="uid://blcu6tjgdxvah"]

[ext_resource type="SpriteFrames" uid="uid://bry1m3sb4n7xr" path="res://assets/Sprites/Animations/32x32/Saw.tres" id="1_sxtut"]
[ext_resource type="Script" uid="uid://b8bs15us03x8b" path="res://Entities/Buildings/simple processing/simple_processing_building.gd" id="2_crhkx"]
[ext_resource type="Texture2D" uid="uid://3hsge6s44m3v" path="res://assets/Sprites/HUD/ConveyorBasedDirection.png" id="2_h3p7u"]
[ext_resource type="Script" uid="uid://2ud046riy2x2" path="res://Entities/Components/Debug/power_info_component.gd" id="3_xv212"]
[ext_resource type="Script" uid="uid://dfrsc76sgl6kk" path="res://Entities/Components/Power/power_receiver_component.gd" id="4_ngot7"]
[ext_resource type="Script" uid="uid://qrdti4s4su0t" path="res://Entities/Buildings/building base/conveyor_based_building_stats.gd" id="5_ry5tw"]
[ext_resource type="Texture2D" uid="uid://tumn8wjiprhh" path="res://assets/Sprites/32x32/SpriteSheets/Saw_sprite_sheet_anim.png" id="6_qi8to"]
[ext_resource type="Script" uid="uid://h5swltwpuw8s" path="res://Entities/Components/Power/power_buffer_component.gd" id="6_umj62"]
[ext_resource type="Script" uid="uid://dta70er7xbp1f" path="res://Entities/Items/item_data.gd" id="7_nskuy"]
[ext_resource type="Resource" uid="uid://bcxlh5grgyr6r" path="res://Entities/Items/Resources/Processed/copper_ingot.tres" id="8_fgc0r"]
[ext_resource type="Resource" uid="uid://cn13mxfv6t6ri" path="res://Entities/Items/Resources/Processed/iron_ingot.tres" id="9_v72hj"]

[sub_resource type="AtlasTexture" id="AtlasTexture_golqk"]
atlas = ExtResource("6_qi8to")
region = Rect2(0, 0, 32, 32)

[resource]
resource_name = "Saw"
script = ExtResource("5_ry5tw")
input_directions = 2
output_directions = 1
transport_speed = 1.0
translation_key = ""
dimensions = Vector2i(1, 1)
is_rotatable = true
sync_animation = false
building_type = 10
components = Array[Resource]([ExtResource("4_ngot7"), ExtResource("3_xv212"), ExtResource("6_umj62")])
cost = Dictionary[ExtResource("7_nskuy"), int]({
ExtResource("8_fgc0r"): 2,
ExtResource("9_v72hj"): 2
})
is_replaceable = true
should_free_after_replace = true
building_group = 2
building_subgroup = 0
player_interactable = true
texture = SubResource("AtlasTexture_golqk")
animation_frames = ExtResource("1_sxtut")
building_script = ExtResource("2_crhkx")
building_info_texture = ExtResource("2_h3p7u")
description = "Saw is used for cutting ingots into rods and wires or refining crystals."
unlocked_by_default = false
menu_order_priority = 0
menu = 0
heat_strength = 0
heat_radius = 0
metadata/_custom_type_script = "uid://qrdti4s4su0t"

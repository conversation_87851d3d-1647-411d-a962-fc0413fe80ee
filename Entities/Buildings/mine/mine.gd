class_name Mine
extends ConveyorBasedBuilding


# TODO: rename to ItemStats
var item_data: ItemData
var _is_mining: bool

var needed_processing_time: float = 5
var total_processing_time: float

# TODO: think about better approach then absolute path
@onready var resource_tilemap: TileMapLayer = $/root/PlanetSurface/Tiles/Resources

@onready var power_receiver_component: PowerReceiverComponent = $Components/PowerReceiverComponent
@onready var power_buffer_component: PowerBufferComponent = $Components/PowerBufferComponent


func _ready() -> void:
	power_buffer_component.init_from(power_receiver_component)

	# Start production when item leaves mine
	item_transport_confirmed.connect(func(_item: Item, _building: Building) -> void: start_production())

	# Set item to produce and start mining if there is valid item to mine
	var data: TileData = resource_tilemap.get_cell_tile_data((tiles_occupied_by_building.front()) as Vector2i)
	if data:
		var resource_kind: StringName = data.get_custom_data("Resource Kind")
		if resource_kind == null:
			return
		item_data = ResourceManager.string_name_to_item_data_map[resource_kind]
		start_production()


func _before_process(delta: float) -> void:
	if _held_item != null:
		return

	if not _is_mining:
		return

	var work_done: float = power_buffer_component.work_based_on(power_receiver_component, delta)
	total_processing_time += work_done * delta
	processing_progress_changed.emit(total_processing_time / needed_processing_time)
	set_animation_progress(total_processing_time / needed_processing_time)

	if total_processing_time > needed_processing_time:
		total_processing_time -= needed_processing_time
		processing_finished.emit()
		end_production()


func start_production() -> void:
	set_animation_progress(0)
	_is_mining = true


func end_production() -> void:
	_held_item = _instantiate_item(item_data)
	held_items_changed.emit(self)
	_is_mining = false

[gd_resource type="Resource" script_class="ItemHandlingBuildingStats" load_steps=13 format=3 uid="uid://b41uoarpo0cmu"]

[ext_resource type="SpriteFrames" uid="uid://xitsonu8ruad" path="res://assets/Sprites/Animations/32x32/Miner.tres" id="1_vn70r"]
[ext_resource type="Script" uid="uid://cfikvvvwxi0hy" path="res://Entities/Buildings/mine/mine.gd" id="2_15huj"]
[ext_resource type="Texture2D" uid="uid://bx5p1ttcts88f" path="res://assets/Sprites/HUD/AllOutput.png" id="2_bqbb1"]
[ext_resource type="Script" uid="uid://c22f0krucy88s" path="res://Scripts/heat_source.gd" id="3_15huj"]
[ext_resource type="Script" uid="uid://qrdti4s4su0t" path="res://Entities/Buildings/building base/conveyor_based_building_stats.gd" id="3_bqbb1"]
[ext_resource type="Script" uid="uid://dfrsc76sgl6kk" path="res://Entities/Components/Power/power_receiver_component.gd" id="4_bqbb1"]
[ext_resource type="Texture2D" uid="uid://ctiwmxcyn186a" path="res://assets/Sprites/32x32/SpriteSheets/Miner_sprite_sheet_anim.png" id="4_pfdbl"]
[ext_resource type="Script" uid="uid://2ud046riy2x2" path="res://Entities/Components/Debug/power_info_component.gd" id="5_pfdbl"]
[ext_resource type="Script" uid="uid://dta70er7xbp1f" path="res://Entities/Items/item_data.gd" id="7_24f5g"]
[ext_resource type="Script" uid="uid://h5swltwpuw8s" path="res://Entities/Components/Power/power_buffer_component.gd" id="7_dsbpb"]
[ext_resource type="Resource" uid="uid://cn13mxfv6t6ri" path="res://Entities/Items/Resources/Processed/iron_ingot.tres" id="9_mbcud"]

[sub_resource type="AtlasTexture" id="AtlasTexture_ykjd5"]
atlas = ExtResource("4_pfdbl")
region = Rect2(0, 0, 32, 32)

[resource]
resource_name = "Mine"
script = ExtResource("3_bqbb1")
input_directions = 0
output_directions = 15
transport_speed = 0.0
translation_key = "MINE"
dimensions = Vector2i(1, 1)
is_rotatable = false
sync_animation = false
building_type = 2
components = Array[Resource]([ExtResource("3_15huj"), ExtResource("4_bqbb1"), ExtResource("5_pfdbl"), ExtResource("7_dsbpb")])
cost = Dictionary[ExtResource("7_24f5g"), int]({
ExtResource("9_mbcud"): 3
})
is_replaceable = true
should_free_after_replace = true
building_group = 0
building_subgroup = 0
player_interactable = true
texture = SubResource("AtlasTexture_ykjd5")
animation_frames = ExtResource("1_vn70r")
building_script = ExtResource("2_15huj")
building_info_texture = ExtResource("2_bqbb1")
description = "By placing these on different ores you are able to generate that ore at the cost of power."
unlocked_by_default = true
menu_order_priority = -2
menu = 0
heat_strength = 5
heat_radius = 6
metadata/_custom_type_script = "uid://qrdti4s4su0t"

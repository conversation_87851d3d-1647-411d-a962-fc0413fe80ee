class_name PowerPlant
extends ItemHandlingBuilding

const MAX_POWER: float = 2000
const POWER_PER_COAL: float = 100
const POWER_OUTPUT_RATE: float = 50

var _held_item: Item = null:
	set(new_value):
		if new_value == null:
			is_occupied = false
		else:
			is_occupied = true
		_held_item = new_value

## Some items last longer this keeps count how long they should last
var _held_item_counter: int = 0
var power_mapping := {
	ItemType.Enum.COAL : 3,
	ItemType.Enum.COAL_BRICK : 8
}

@onready var power_provider_component: PowerProviderComponent = $Components/PowerProviderComponent
@onready var power_buffer_component: PowerBufferComponent = $Components/PowerBufferComponent
@onready var animated_sprite_2d: AnimatedSprite2D = $AnimatedSprite2D


func _ready() -> void:
	power_provider_component.power_provided.connect(_power_drawn)
	power_provider_component.power_rate = POWER_OUTPUT_RATE
	power_provider_component.power_output_priority = PowerOutputPriority.Enum.GENERAL

	power_buffer_component.max_power_stored = MAX_POWER


func _on_push_item(item: Item, _sender: Building) -> void:
	if item == null:
		return

	if _held_item != null:
		return

	item_received.emit(item, self)


func _is_item_acceptable(_item: Item) -> bool:
	return _item.item_data.type in power_mapping.keys()


func get_output_items() -> Array[Item]:
	if _held_item:
		return [_held_item]
	return []


func _on_item_received(item: Item, building: Building) -> void:
	if item == _held_item:
		item_transport_confirmed.emit(_held_item, building)
		_held_item = null


func _on_item_transport_confirm(item: Item, building: Building, _skip_animation: bool = false) -> void:
	if building == self:
		_held_item = item
		item.reparent(building)
		item.move_to(global_position, 0, _skip_animation)


func _before_process(_delta: float) -> void:
	if Engine.is_editor_hint():
		return

	if _held_item_counter > 0:
		if power_buffer_component.stored_power + POWER_PER_COAL < MAX_POWER:
			power_buffer_component.stored_power += POWER_PER_COAL
			_held_item_counter -= 1

	# Wait for item
	if _held_item == null:
		return

	# Wait for animation to finish (item to arrive)
	if _held_item.is_moving():
		return

	if _held_item_counter == 0:
		_held_item_counter = power_mapping.get(_held_item.item_data.type, 0)
		_held_item.queue_free()
		_held_item = null

	if _held_item:
		has_item.emit(_held_item, self)


func _power_drawn(power: Variant, delta: Variant) -> void:
	power_buffer_component.stored_power -= power * delta

	var power_available: float = power_buffer_component.stored_power
	var power_stored: float = power_buffer_component.max_power_stored
	var ratio: float = power_available / power_stored

	if ratio < 0.2:
		animated_sprite_2d.set_frame_and_progress(0, 0)
	elif ratio < 0.8:
		animated_sprite_2d.set_frame_and_progress(1, 0)
	else:
		animated_sprite_2d.set_frame_and_progress(2, 0)

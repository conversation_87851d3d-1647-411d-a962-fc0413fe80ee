@tool
class_name ItemInfoComponent
extends BaseInfoComponent
## Adds item info to all items


func _ready() -> void:
	name = "Item Info"

	# Always show labels in editor
	if Engine.is_editor_hint():
		show_labels()
		return

	init(DebugManager.item_debug, DebugManager.item_debug_changed)


func _generate_labels() -> void:
	var item: Item = get_parent() as Item

	if item == null:
		push_error("Item info component not used on Item")
		return

	var item_name: Label = Label.new()
	if item.item_data:
		item_name.text = item.item_data.resource_name
	else:
		item_name.text = "ITEM_NAME"

	if not Engine.is_editor_hint():
		item.stats_reloaded.connect(func() -> void: item_name.text = item.item_data.resource_name)

	labels.append(item_name)

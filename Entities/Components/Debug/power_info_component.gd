@tool
class_name PowerInfoComponent
extends BaseInfoComponent
## Adds power info to all power components


func _ready() -> void:
	name = "Power Info"

	# Always show labels in editor
	if Engine.is_editor_hint():
		show_labels()
		return

	init(DebugManager.power_debug, DebugManager.power_debug_changed)


func _generate_labels() -> void:
	var providers: Array[PowerProviderComponent] = []
	var receivers: Array[PowerReceiverComponent] = []
	var buffers: Array[PowerBufferComponent] = []

	for child in get_parent().get_children():
		if child is PowerProviderComponent:
			providers.append(child)
		if child is PowerReceiverComponent:
			receivers.append(child)
		if child is PowerBufferComponent:
			buffers.append(child)

	var main_label: Label = Label.new()
	main_label.text = self.name
	labels.append(main_label)

	var parent: Node2D = get_parent()

	# Code to execute when in game.
	if parent is PowerNetwork:
		var p_label: Label = Label.new()
		var r_label: Label = Label.new()
		var t_label: Label = Label.new()
		p_label.text = "P: [Providing]"
		r_label.text = "R: [Receiving]"
		t_label.text = "T: [Transfering]"
		if not Engine.is_editor_hint():
			(parent as PowerNetwork).power_recalculated.connect(
				func(
					total_available_power: float,
					total_required_power: float,
					total_power_transferred: float,
					_delta: float
				) -> void:
					p_label.text = "P: %.1f" % total_available_power
					r_label.text = "R: %.1f" % total_required_power
					t_label.text = "T: %.1f" % total_power_transferred
			)
		labels.append(p_label)
		labels.append(r_label)
		labels.append(t_label)

	for buffer: PowerBufferComponent in buffers:
		var battery_label: Label = Label.new()
		if Engine.is_editor_hint():
			battery_label.text = "C: [Capacity]"
		else:
			battery_label.text = "C: [Capacity]"
			buffer.stored_energy_changed.connect(
				func() -> void: battery_label.text = ("C: %.1f / %.1f" % [buffer.stored_power, buffer.max_power_stored])
			)
		labels.append(battery_label)

	for provider: PowerProviderComponent in providers:
		var provider_label: Label = Label.new()
		if Engine.is_editor_hint():
			provider_label.text = "P: [Providing]"
		else:
			provider_label.text = "P: [Providing]"
			provider.power_provided.connect(
				func(power: float, _delta: float) -> void: provider_label.text = "P: %.1f / %.1f" % [power, provider.power_rate]
			)
		labels.append(provider_label)

	for receiver: PowerReceiverComponent in receivers:
		var receiver_label: Label = Label.new()
		if Engine.is_editor_hint():
			receiver_label.text = "R: [Receiving]"
		else:
			receiver_label.text = "R: [Receiving]"
			receiver.power_received.connect(
				func(power: float, _delta: float) -> void: receiver_label.text = "R: %.1f / %.1f" % [power, receiver.power_rate]
			)
		labels.append(receiver_label)

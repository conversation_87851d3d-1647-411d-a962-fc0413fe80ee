@icon("res://assets/IconGodotNode/node_2D/icon_magnifier.png")
class_name BaseInfoComponent
extends Component

var generated: bool = false
var labels: Array[Label] = []


## Handles logic behind value changes
func init(init_value: bool, change_signal: Signal) -> void:
	# Add labels on start if enabled
	if init_value:
		show_labels()

	# Add or remove labels on state change
	change_signal.connect(
		func(new_value: bool) -> void:
			if new_value:
				show_labels()
			else:
				hide_labels()
	)

	_fix_rotation.call_deferred()


func _fix_rotation() -> void:
	var parent: Node2D = get_parent() as Node2D
	rotation = -parent.global_rotation


func _generate_labels() -> void:
	pass


func _add_labels() -> void:
	var container: VBoxContainer = VBoxContainer.new()
	container.z_index = 100
	if is_inside_tree() == true:
		add_child(container)
		for label in labels:
			label.add_theme_font_size_override("font_size", 4)
			container.add_child(label)


func show_labels() -> void:
	if not generated:
		_generate_labels()
		_add_labels()
		generated = true

	for label in labels:
		label.visible = true


func hide_labels() -> void:
	for label in labels:
		label.visible = false

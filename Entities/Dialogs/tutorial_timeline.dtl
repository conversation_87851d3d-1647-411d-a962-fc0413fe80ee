[style name="tutorial_style"]
join helper (<PERSON>) center
helper: Ok let's get this over with, my name is <PERSON>.<PERSON><PERSON><PERSON><PERSON>
helper (Unamussed): While you're looking around i'll just be collecting some basic resources.
helper: You can see your available resources in the top right.
helper (Happy): You will use these resources to repair the factory you broke.
helper (Unamussed): While I could collect all the resources for you lets speed it up.
helper (<PERSON>): Let's start of by building a mine.
set {progress_type} = "press_i"
helper (<PERSON>): Open up your building list by pressing "I"
set {progress_type} = "click"
helper (Relaxed): If you click on any building here you begin to build it.
helper (Cat): If you wish to add a building to your toolbar hover it in the menu and press any number.
helper: You can change what building you are building by clicking on it in the toolbar.
set {progress_type} = "collect_iron"
helper (Smile): Ok put down a mine and collect some iron yourself.
set {progress_type} = "click"
if {iron_collected} == "raw":
	helper (Cat): Yes ok that one might be my bad.
	set {progress_type} = "collect_iron"
	label failed_iron
	helper (<PERSON>): You will have to smelt the iron in a smeltery otherwise it's useless
	if {iron_collected} == "ingot":
		set {progress_type} = "click"
		jump got_iron
	else:
		helper (Unamussed): Nope thats not the right thing
		jump failed_iron
elif {iron_collected} == "ingot":
	helper (Shocked): Wow look at you. I didn't even have to explain the smeltery.
label got_iron
helper (Happy): Ok now that we have iron flowing in you can really start repairing the factory.
helper (Happy): Under your available resources you can find a set of goals which this factory should meet.
helper (Relaxed): If you just read i'm sure you will figure it out.
helper (Relaxed): You don't need anyone explaining assemblers, inserters or any other building.
- Yes I got this no worries
	helper (Happy): Finally a decent human. I'll keep on mining and you repair the factory.
	[end_timeline]
- What do you mean I don't need help?
helper (Unamussed): I mean you can read right? Just read what you need to do or look in the database top left.
helper (Unamussed): You don't expect me to hold your hand through all this do you?
- No I can read
	helper (Smile): Ok good well good luck
	[end_timeline]
- Yes I kinda did
helper (Facepalm): Gaaaahhh.
helper (Unamussed): Fine I'll help with one more task and we will be done ok?
helper (Unamussed): Let's see here... Ok let's make makeshift rockets.
helper (Unamussed): We will need to do a couple of things so I will just list them and then you hit me up when you are done.
label steps
helper (Unamussed): Step 1 is to mine some coal
helper (Unamussed): Step 2 is to mine iron and smelt it into ingots
helper (Unamussed): Step 3 is to turn those ingots into plates using the press
helper (Unamussed): Step 4 build an assembler and select the right recipe by clicking it
helper (Unamussed): Step 5 get the resources into the assembler. Since assembler is big you will need to use inserters to take items in and out
helper (Unamussed): Step 6 bring the rockets to the sink
helper (Unamussed): There, we are done. Do you want me to repeat the steps again?
- Yes please
	jump steps
- Nah im good
helper (Relaxed): Ok great, one last thing you were lucky that you didnt break the powerplants but you might not be as lucky next time.
helper (Relaxed): In case you don't have any you can just make them and feed them coal with inserters.
helper (Relaxed): Anyway thats all from me.

[gd_resource type="Resource" script_class="DialogicStyle" load_steps=20 format=3 uid="uid://cwpk7k3ixn1yr"]

[ext_resource type="Script" uid="uid://c0222foa6ybos" path="res://addons/dialogic/Resources/dialogic_style_layer.gd" id="1_75nve"]
[ext_resource type="PackedScene" uid="uid://c1k5m0w3r40xf" path="res://addons/dialogic/Modules/DefaultLayoutParts/Layer_FullBackground/full_background_layer.tscn" id="2_4wgkb"]
[ext_resource type="PackedScene" uid="uid://cdducpjul7y1k" path="res://Entities/Dialogs/InputCatcher/custom_input_catcher.tscn" id="3_75nve"]
[ext_resource type="PackedScene" uid="uid://s8hfkc1y858f" path="res://Entities/Dialogs/TextboxWithPortrait/custom_textbox_with_portrait_.tscn" id="4_75nve"]
[ext_resource type="PackedScene" uid="uid://dsbwnp5hegnu3" path="res://addons/dialogic/Modules/DefaultLayoutParts/Layer_Glossary/glossary_popup_layer.tscn" id="5_gvmba"]
[ext_resource type="PackedScene" uid="uid://dhk6j6eb6e3q" path="res://addons/dialogic/Modules/DefaultLayoutParts/Layer_VN_Choices/vn_choice_layer.tscn" id="6_nqxhu"]
[ext_resource type="PackedScene" uid="uid://cvgf4c6gg0tsy" path="res://addons/dialogic/Modules/DefaultLayoutParts/Layer_TextInput/text_input_layer.tscn" id="7_t6wje"]
[ext_resource type="PackedScene" uid="uid://lx24i8fl6uo" path="res://addons/dialogic/Modules/DefaultLayoutParts/Layer_History/history_layer.tscn" id="8_kius5"]
[ext_resource type="PackedScene" uid="uid://cy1y14inwkplb" path="res://addons/dialogic/Modules/DefaultLayoutParts/Layer_VN_Portraits/vn_portrait_layer.tscn" id="9_4wgkb"]
[ext_resource type="Script" uid="uid://dd8j2eqp5yj2j" path="res://addons/dialogic/Resources/dialogic_style.gd" id="9_7x0em"]

[sub_resource type="Resource" id="Resource_aoxiv"]
script = ExtResource("1_75nve")
overrides = {}

[sub_resource type="Resource" id="Resource_x1u5n"]
script = ExtResource("1_75nve")
scene = ExtResource("2_4wgkb")
overrides = {
"disabled": "true"
}

[sub_resource type="Resource" id="Resource_6t6n0"]
script = ExtResource("1_75nve")
scene = ExtResource("3_75nve")
overrides = {}

[sub_resource type="Resource" id="Resource_44lvv"]
script = ExtResource("1_75nve")
scene = ExtResource("4_75nve")
overrides = {
"box_modulate_custom_color": "Color(0.113462, 0.308594, 0.0855865, 1)",
"box_modulate_global_color": "false",
"box_panel": "\"res://assets/Styles/GenericButtonFilled.tres\"",
"box_size": "Vector2(600, 200)",
"name_label_color_mode": "1",
"portrait_bg_modulate": "Color(0, 0, 0, 0)",
"portrait_stretch_factor": "0.5",
"text_use_global_size": "false"
}

[sub_resource type="Resource" id="Resource_ah2xg"]
script = ExtResource("1_75nve")
scene = ExtResource("5_gvmba")
overrides = {}

[sub_resource type="Resource" id="Resource_wnlga"]
script = ExtResource("1_75nve")
scene = ExtResource("6_nqxhu")
overrides = {}

[sub_resource type="Resource" id="Resource_rrfyt"]
script = ExtResource("1_75nve")
scene = ExtResource("7_t6wje")
overrides = {}

[sub_resource type="Resource" id="Resource_urqiw"]
script = ExtResource("1_75nve")
scene = ExtResource("8_kius5")
overrides = {}

[sub_resource type="Resource" id="Resource_c4sas"]
script = ExtResource("1_75nve")
scene = ExtResource("9_4wgkb")
overrides = {
"disabled": "true"
}

[resource]
script = ExtResource("9_7x0em")
name = "tutorial_style"
layer_list = Array[String](["10", "11", "12", "13", "14", "15", "16", "17"])
layer_info = {
"": SubResource("Resource_aoxiv"),
"10": SubResource("Resource_x1u5n"),
"11": SubResource("Resource_6t6n0"),
"12": SubResource("Resource_44lvv"),
"13": SubResource("Resource_ah2xg"),
"14": SubResource("Resource_wnlga"),
"15": SubResource("Resource_rrfyt"),
"16": SubResource("Resource_urqiw"),
"17": SubResource("Resource_c4sas")
}
base_overrides = {}
layers = Array[ExtResource("1_75nve")]([])
metadata/_latest_layer = "17"

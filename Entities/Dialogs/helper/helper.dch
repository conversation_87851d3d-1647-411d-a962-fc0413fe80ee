{
"@path": "res://addons/dialogic/Resources/character.gd",
"@subpath": NodePath(""),
&"_translation_id": "",
&"color": Color(0.584961, 1, 0.375, 1),
&"custom_info": {
"sound_mood_default": "",
"sound_moods": {
"Brr": {
"mode": 1,
"name": "Brr",
"pitch_base": 2.0,
"pitch_variance": 0.0,
"skip_characters": 0.0,
"sound_path": "res://Entities/Dialogs/asistent/blipSelect.wav",
"volume_base": 0.0,
"volume_variance": 0.0
}
},
"style": ""
},
&"default_portrait": "Blank",
&"description": "Tries to help you with all its might.",
&"display_name": "helper",
&"mirror": false,
&"nicknames": ["Helpie"],
&"offset": Vector2(0, 0),
&"portraits": {
"Blank": {
"export_overrides": {
"bobing": "true",
"image": "\"res://Entities/Dialogs/helper/Robo-talk-Sheet.png\""
},
"mirror": false,
"offset": Vector2(0, 0),
"scale": 0.5,
"scene": "res://Entities/Dialogs/helper/expressions/blank.tscn"
},
"Cat": {
"export_overrides": {
"image": ""
},
"mirror": false,
"offset": Vector2(0, 0),
"scale": 0.5,
"scene": "res://Entities/Dialogs/helper/expressions/cat.tscn"
},
"Facepalm": {
"export_overrides": {
"image": ""
},
"mirror": false,
"offset": Vector2(0, 0),
"scale": 0.5,
"scene": "res://Entities/Dialogs/helper/expressions/facepalm.tscn"
},
"Frown": {
"export_overrides": {
"image": ""
},
"mirror": false,
"offset": Vector2(0, 0),
"scale": 0.5,
"scene": "res://Entities/Dialogs/helper/expressions/frown.tscn"
},
"Happy": {
"export_overrides": {
"image": ""
},
"mirror": false,
"offset": Vector2(0, 0),
"scale": 0.5,
"scene": "res://Entities/Dialogs/helper/expressions/happy.tscn"
},
"Loading": {
"export_overrides": {
"image": ""
},
"mirror": false,
"offset": Vector2(0, 0),
"scale": 0.5,
"scene": "res://Entities/Dialogs/helper/expressions/loading.tscn"
},
"Relaxed": {
"export_overrides": {
"image": ""
},
"mirror": false,
"offset": Vector2(0, 0),
"scale": 0.5,
"scene": "res://Entities/Dialogs/helper/expressions/relaxed.tscn"
},
"Sad": {
"export_overrides": {
"image": ""
},
"mirror": false,
"offset": Vector2(0, 0),
"scale": 0.5,
"scene": "res://Entities/Dialogs/helper/expressions/sad.tscn"
},
"Shocked": {
"export_overrides": {
"image": ""
},
"mirror": false,
"offset": Vector2(0, 0),
"scale": 0.5,
"scene": "res://Entities/Dialogs/helper/expressions/shocked.tscn"
},
"Smile": {
"export_overrides": {
"image": ""
},
"mirror": false,
"offset": Vector2(0, 0),
"scale": 0.5,
"scene": "res://Entities/Dialogs/helper/expressions/smile.tscn"
},
"Unamussed": {
"export_overrides": {
"image": ""
},
"mirror": false,
"offset": Vector2(0, 0),
"scale": 0.5,
"scene": "res://Entities/Dialogs/helper/expressions/unamussed.tscn"
},
"XD": {
"export_overrides": {
"image": ""
},
"mirror": false,
"offset": Vector2(0, 0),
"scale": 0.5,
"scene": "res://Entities/Dialogs/helper/expressions/XD.tscn"
}
},
&"scale": 1.0
}
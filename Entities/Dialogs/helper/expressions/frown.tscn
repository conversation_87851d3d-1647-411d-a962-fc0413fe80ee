[gd_scene load_steps=9 format=3 uid="uid://bwypy16t14bas"]

[ext_resource type="Script" uid="uid://c1k8bet6qw2cg" path="res://Entities/Dialogs/helper/animated_portrait.gd" id="1_wjec0"]
[ext_resource type="Texture2D" uid="uid://7l13q2c8fcf" path="res://assets/Sprites/32x32/SpriteSheets/Robo-talk-Sheet.png" id="2_iv3v3"]

[sub_resource type="AtlasTexture" id="AtlasTexture_wjec0"]
atlas = ExtResource("2_iv3v3")
region = Rect2(0, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_iv3v3"]
atlas = ExtResource("2_iv3v3")
region = Rect2(64, 128, 64, 64)

[sub_resource type="SpriteFrames" id="SpriteFrames_qcu80"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_wjec0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_iv3v3")
}],
"loop": true,
"name": &"default",
"speed": 5.0
}]

[sub_resource type="Animation" id="Animation_hpxxj"]
resource_name = "Bobing"
length = 1.2
loop_mode = 1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.3, 0.9, 1.2),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(0, 0), Vector2(0, 1), Vector2(0, -1), Vector2(0, 0)]
}

[sub_resource type="Animation" id="Animation_5wk6r"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0, 0)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_08a4p"]
_data = {
&"Bobing": SubResource("Animation_hpxxj"),
&"RESET": SubResource("Animation_5wk6r")
}

[node name="Smile" type="Node2D"]
script = ExtResource("1_wjec0")

[node name="Sprite" type="AnimatedSprite2D" parent="."]
sprite_frames = SubResource("SpriteFrames_qcu80")
frame_progress = 0.129456
offset = Vector2(0, -82)

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_08a4p")
}

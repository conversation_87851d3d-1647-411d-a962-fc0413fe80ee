[gd_scene load_steps=9 format=3 uid="uid://bfplenq2dymgv"]

[ext_resource type="Script" uid="uid://c1k8bet6qw2cg" path="res://Entities/Dialogs/helper/animated_portrait.gd" id="1_kt17a"]
[ext_resource type="Texture2D" uid="uid://7l13q2c8fcf" path="res://assets/Sprites/32x32/SpriteSheets/Robo-talk-Sheet.png" id="2_hpxxj"]

[sub_resource type="AtlasTexture" id="AtlasTexture_5wk6r"]
atlas = ExtResource("2_hpxxj")
region = Rect2(0, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_08a4p"]
atlas = ExtResource("2_hpxxj")
region = Rect2(64, 0, 64, 64)

[sub_resource type="SpriteFrames" id="SpriteFrames_nmnu4"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_5wk6r")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_08a4p")
}],
"loop": true,
"name": &"default",
"speed": 5.0
}]

[sub_resource type="Animation" id="Animation_hpxxj"]
resource_name = "Bobing"
length = 1.2
loop_mode = 1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.3, 0.9, 1.2),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(0, 0), Vector2(0, 1), Vector2(0, -1), Vector2(0, 0)]
}

[sub_resource type="Animation" id="Animation_5wk6r"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0, 0)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_08a4p"]
_data = {
&"Bobing": SubResource("Animation_hpxxj"),
&"RESET": SubResource("Animation_5wk6r")
}

[node name="Blank" type="Node2D"]
script = ExtResource("1_kt17a")

[node name="Sprite" type="AnimatedSprite2D" parent="."]
sprite_frames = SubResource("SpriteFrames_nmnu4")
frame_progress = 0.262247
offset = Vector2(0, -82)

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_08a4p")
}

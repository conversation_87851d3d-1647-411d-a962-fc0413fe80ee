[gd_scene load_steps=3 format=3 uid="uid://ba2ok888yp5v4"]

[ext_resource type="Script" uid="uid://d2s7r1nqci5th" path="res://Entities/NodeMap/space_camera.gd" id="1_ocwkk"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_t0jle"]

[node name="Camera2D" type="Camera2D"]
script = ExtResource("1_ocwkk")
max_zoom_in = 2.0

[node name="Area2D" type="Area2D" parent="."]
collision_layer = 2
collision_mask = 2
input_pickable = false

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]
shape = SubResource("RectangleShape2D_t0jle")

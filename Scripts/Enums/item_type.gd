class_name ItemType

enum Enum {
	CIRCUIT_BOARD,
	<PERSON><PERSON><PERSON><PERSON><PERSON>_BALL,
	<PERSON>LOUD_ORE,
	COAL,
	COAL_BRICK,
	COBALT_INGOT,
	COBALT_ORE,
	COPPER_INGOT,
	COPPER_ORE,
	COPPER_WIRE,
	CRYSTALENIUM_ORE,
	CUT_CRYSTALENIUM,
	GOLD_INGOT,
	GOLD_ORE,
	IRON_INGOT,
	IRON_ORE,
	IRON_PLATE,
	IRON_RODS,
	MAKESHIFT_ROCKET,
	PYROMAGMATENARIUM_CORE,
	PYRO<PERSON>GMAT<PERSON>ARIUM_ORE,
	ROBOT,
	SAND,
	SILICON,
	URANIUM_ORE,
	URANIUM_PELLET,
	# im sorry for not alphabetically but i dont wnat to fix everything breaking
	COBALT_RODS
}

const storables: Array[Enum] = [Enum.IRON_INGOT, Enum.COPPER_INGOT]


static func to_name(value: Enum) -> StringName:
	for key: String in Enum:
		if Enum[key] == value:
			return key
	return str(value)


static func enum_to_string(value: Enum) -> String:
	var name: String = Enum.keys()[value]
	name = name.to_lower().replace("_", " ")
	return name.capitalize()


static func is_storable(item: Enum) -> bool:
	return item in storables


static func get_storable_items() -> Array[Enum]:
	return storables

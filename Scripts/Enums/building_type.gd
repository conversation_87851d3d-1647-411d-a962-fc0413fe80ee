class_name BuildingType

enum Enum {
	NONE,
	CONVEYOR_BELT,
	MINE,
	POWER_BATTERY,
	POWER_CONSUMER,
	POWER_GENERATOR,
	POWER_NETWORK,
	SINK,
	SMELTERY,
	PRESS,
	SAW,
	ASSEMBLER,
	# Debug buildings
	ITEM_GENERATOR,
	# More unsorted
	CATAPULT,
	INSERTER,
}


static func to_name(value: Enum) -> StringName:
	for key: String in Enum:
		if Enum[key] == value:
			return key
	return str(value)


static func enum_to_string(value: Enum) -> String:
	var name: String = Enum.keys()[value]
	name = name.to_lower().replace("_", " ")
	return name.capitalize()

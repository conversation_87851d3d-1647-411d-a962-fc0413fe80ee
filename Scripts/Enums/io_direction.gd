class_name IODirection

enum Enum {
	FRONT = 1 << 0,
	BACK = 1 << 1,
	LEFT = 1 << 2,
	RIGHT = 1 << 3,
}


static func directions_from_flags(flags: int) -> Array[Enum]:
	var directions: Array[Enum]
	# TODO: research more clever way of how to do it
	if flags & Enum.FRONT != 0:
		directions.append(Enum.FRONT)
	if flags & Enum.BACK != 0:
		directions.append(Enum.BACK)
	if flags & Enum.LEFT != 0:
		directions.append(Enum.LEFT)
	if flags & Enum.RIGHT != 0:
		directions.append(Enum.RIGHT)
	return directions


static func to_name(value: Enum) -> StringName:
	for key: String in Enum:
		if Enum[key] == value:
			return key
	return str(value)

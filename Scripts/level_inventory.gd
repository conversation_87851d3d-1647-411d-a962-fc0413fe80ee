class_name ResourceInventory
extends Resource

## Emitted when the count of units of some resource type has changed.
@warning_ignore("unused_signal")
signal resource_amount_changed(type: ItemType.Enum, new_amount: int)

@export var resources: Dictionary[ItemType.Enum, int]


func _ready() -> void:
	for item in ItemType.get_storable_items():
		if item not in resources:
			resources[item] = 0


func add_resource(amount: int, type: ItemType.Enum) -> void:
	if type in resources:
		resources[type] += amount
		SurfaceSignalBus.resource_amount_changed.emit(type, resources[type])


func consume_resources(cost: Dictionary) -> bool:
	if !sufficient_resources(cost):
		return false
	for item: ItemType.Enum in cost.keys():
		resources[item] -= cost[item]
		SurfaceSignalBus.resource_amount_changed.emit(item, resources[item])
	return true


func sufficient_resources(cost: Dictionary[ItemType.Enum, int]) -> bool:
	for key: ItemType.Enum in cost.keys():
		if key not in resources:
			LogManager.warn("unknown resource %s" % key, self)
			return false

		if cost[key] > resources[key]:
			# Commented out because placement ghost
			# LogManager.info('not enough resources', self)
			return false
	return true

@tool
class_name CameraLimiter
extends ReferenceRect

@export_range(1.0, 10.0, 0.25) var limiter_multiplier: float = 1.0

var midpoint: Vector2 = Vector2.ZERO


func scale_around_used_rect(used_rect_in_world: Rect2) -> void:
	midpoint = used_rect_in_world.get_center()

	var background: SpaceBackground = $Background
	background.size = used_rect_in_world.size * limiter_multiplier
	background.resolution = background.size

	size = background.size
	global_position = midpoint - (size / 2)
	background.position = Vector2(background.size / 2)

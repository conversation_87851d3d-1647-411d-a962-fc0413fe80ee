class_name PlayerActions
extends Node2D

const CAMERA_COLLISION_MASK: int = 2

@export var navigator_offset: int = 100
@export var delta_multiplier: float = 2.5

@onready var tilemap: TileMapLayer = %Tiles/Ground
@onready var camera: SpaceCamera = $"../Camera2D"
@onready var map_navigator: Node2D = $MapNavigator
@onready var map_navigator_sprite: Node2D = $MapNavigator/Sprite2D


func _physics_process(delta: float) -> void:
	var used_rect: Rect2i = tilemap.get_used_rect()
	var used_rect_in_world: Rect2 = Rect2(
		used_rect.position * tilemap.tile_set.tile_size,
		used_rect.size * tilemap.tile_set.tile_size,
	)

	if not used_rect_in_world.intersects(camera.get_camera_rect()):
		map_navigator.show()
	else:
		map_navigator.hide()

	if map_navigator.visible:
		var midpoint: Vector2 = used_rect_in_world.get_center()
		var screen_center_point: Vector2 = camera.get_screen_center_position()
		

		var intersection_point: Vector2 = _calculate_intersection_point(
			camera.get_camera_rect(),
			midpoint - camera.get_camera_rect().get_center(),
		)

		map_navigator_sprite.look_at(midpoint)
		var step: Vector2 = (intersection_point - screen_center_point).normalized()
		var target_position: Vector2 = intersection_point - (step * navigator_offset)

		var tween: Tween = create_tween()
		tween.set_loops(1)
		tween.tween_property(map_navigator, "global_position", target_position, delta * delta_multiplier)

	queue_redraw()


func _unhandled_input(event: InputEvent) -> void:
	if StateManager.state != StateManager.States.PLAY:
		return

	if event is not InputEventMouseButton:
		return

	if event.is_action_pressed("mouse_rmb"):
		StateManager.active_building = null

	var tile_coords: Vector2 = tilemap.local_to_map(to_local(get_global_mouse_position()))
	var building: Building = BuildingModeManager.get_building_at_position(tile_coords)
	if not building:
		StateManager.active_building = null
		return

	if event.is_action_pressed("mouse_lmb"):
		StateManager.active_building = building


# This was create by the chat, but it actually works, so it is fine.
## Casts a ray from the center of a rectangle in a given direction
## and finds where it exits the rectangle boundary.
##
## Arguments:
##   rect: the Rect2 we are working with
##   direction: the Vector2 that defines the ray direction (does not need to be normalized)
##   eps: small tolerance value to avoid division by zero
##
## Returns:
##   The Vector2 point on the rectangle edge where the ray exits.
func _calculate_intersection_point(rect: Rect2, direction: Vector2, eps := 1e-9) -> Vector2:
	if direction.length_squared() <= eps * eps:
		return Vector2.ZERO

	# Half extents of the rectangle (distance from center to each side)
	var hx: float = rect.size.x * 0.5
	var hy: float = rect.size.y * 0.5

	# Normalize direction relative to half extents:
	#   scale_x tells how quickly we reach the vertical edges,
	#   scale_y tells how quickly we reach the horizontal edges.
	var sx: float = abs(direction.x) / max(hx, eps)
	var sy: float = abs(direction.y) / max(hy, eps)

	# We need the smallest multiplier t so that the ray touches *any* boundary.
	# That happens when either the x displacement reaches half_width
	# or the y displacement reaches half_height — whichever comes first.
	# So t = 1 / max(scale_x, scale_y).
	var t: float = 1.0 / max(sx, sy)

	var hit: Vector2 = rect.get_center() + direction * t

	return hit


## This draws the necessary geometry to verify the correct functionality of map
## navigator. Simply call this in the _draw method.
func _debug_draw() -> void:
	var used_rect: Rect2i = tilemap.get_used_rect()
	var used_rect_in_world: Rect2 = Rect2(
		used_rect.position * tilemap.tile_set.tile_size,
		used_rect.size * tilemap.tile_set.tile_size,
	)
	var midpoint: Vector2 = used_rect_in_world.position + (used_rect_in_world.size / 2)
	draw_rect(used_rect_in_world, Color.RED, false, 5.0)
	draw_rect(camera.get_camera_rect(), Color.AQUA, false, 5.0)
	draw_line(camera.get_screen_center_position(), midpoint, Color.WHITE, 5.0)

	var intersection_point: Vector2 = _calculate_intersection_point(
		camera.get_camera_rect(), midpoint - camera.get_camera_rect().get_center()
	)
	draw_circle(intersection_point, 5.0, Color.WHITE, true, 5.0)

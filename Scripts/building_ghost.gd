extends Node2D

const TRANSPARENT_RED: Color = Color(Color.RED, 0.5)
const TRANSPARENT_GREEN: Color = Color(Color.GREEN, 0.5)
const TRANSPARENT_YELLOW: Color = Color(Color.YELLOW, 0.5)

const BUILD_INFO_TRANSPARENCY_VALUE: float = 0.75

var handle_ghost: bool = false
var ghost: Node = null
var level_inventory: ResourceInventory

@onready var builder: BuildingBuilder = $".."
@onready var level_manager: LevelHandler = $"../.."
@onready var ground: TileMapLayer = %Tiles/Ground

@onready var sprite: Sprite2D = $Sprite2D
@onready var building_info_sprite: Sprite2D = $BuildingInfo


func _ready() -> void:
	StateManager.state_changed.connect(_on_state_changed)
	level_inventory = SaveManager.get_planet_data().get_inventory()


func _on_state_changed(new_state: StateManager.States) -> void:
	match new_state:
		StateManager.States.BUILD, StateManager.States.PLUMBING:
			_ghost_activate()
		_:
			_ghost_deactivate()


func _ghost_activate() -> void:
	if not BuildingModeManager.selected_building_stats:
		return
	_update_position_and_rotation()
	_update_color()

	sprite.texture = BuildingModeManager.selected_building_stats.texture
	building_info_sprite.texture = BuildingModeManager.selected_building_stats.building_info_texture
	if building_info_sprite.texture:
		building_info_sprite.modulate.a = BUILD_INFO_TRANSPARENCY_VALUE
		building_info_sprite.show()

	show()
	set_process(true)


func _ghost_deactivate() -> void:
	hide()
	set_process(false)


func _update_position_and_rotation() -> void:
	position = ground.map_to_local(ground.local_to_map(get_global_mouse_position()))
	var tile_size: Vector2i = ground.tile_set.tile_size / 2
	var offset: Vector2i = tile_size * (BuildingModeManager.selected_building_stats.dimensions - Vector2i.ONE)
	position += Vector2(offset)
	if BuildingModeManager.selected_building_stats.is_rotatable:
		rotation_degrees = BuildingModeManager.building_rotation
	else:
		rotation_degrees = 0


func _update_color() -> void:
	var ground_position: Vector2i = ground.local_to_map(get_global_mouse_position())
	var result: Dictionary = BuildingModeManager.can_build_at_position(ground, ground_position)
	sprite.modulate = result["color"]

	var building_cost: Dictionary[ItemData, int] = BuildingModeManager.selected_building_stats.cost
	
	# TODO Change this so it does not need to be converted into types
	var type_building_cost: Dictionary[ItemType.Enum, int] = {}
	for key: ItemData in building_cost:
		type_building_cost[key.type] = building_cost[key]
	
	if not level_manager.inventory.sufficient_resources(type_building_cost):
		sprite.modulate = Color(1, 0, 0, 0.5)


func _physics_process(_delta: float) -> void:
	if StateManager.state == StateManager.States.PLAY:
		return
	if BuildingModeManager.selected_building_stats == null:
		return
	_update_position_and_rotation()
	_update_color()

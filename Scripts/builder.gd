class_name BuildingBuilder
extends Node2D

@warning_ignore("unused_signal")
signal building_blocked(what: String, why: String)

const BUILDING_PREFAB: PackedScene = preload("res://Entities/Buildings/building base/building.tscn")
const CARDINAL_VECTORS = [Vector2i(1, 0), Vector2i(-1, 0), Vector2i(0, 1), Vector2i(0, -1)]

var is_dragging: bool = false
var start_building_tile: Vector2i = Vector2i.ZERO
var last_mouse_tile: Vector2i = Vector2i.ZERO
var new_mouse_tile: Vector2i = Vector2i.ZERO
var direction_vector: Vector2i = Vector2i.ZERO

@onready var tilemap: TileMapLayer = %Tiles/Ground
@onready var shadow_map: Shadows = %Tiles/Shadows
@onready var buildings: Node2D = $Buildings
@onready var demolisher: Demolisher = %Demolisher

@onready var level_manager: LevelHandler = $".."
@onready var heat_manager: HeatHandler = %HeatHandler


##Scale the building collider to fit building dimensions
func _building_scaling(building_prefab: Node, building_data: BuildingData) -> void:
	var tile_size: Vector2i = tilemap.tile_set.tile_size
	var collider: CollisionShape2D = building_prefab.find_child("CollisionShape2D", true, false)

	# Ensure the shape is initialized issue when instancing even if shape is assigned in inspector
	if collider.shape == null:
		collider.shape = RectangleShape2D.new()

	# Check type and set size
	if collider.shape is RectangleShape2D:
		var shape: RectangleShape2D = collider.shape
		shape.size = building_data.building_stats.dimensions * tile_size
	else:
		LogManager.error("Shape is not RectangleShape2D: %s" % collider.shape, self)


func _cancel_building() -> void:
	is_dragging = false
	BuildingModeManager.building_rotation = 0


func _instantiate_building(building_data: BuildingData, is_replacing: bool = false, silent: bool = false) -> void:
	var world_position: Vector2i = building_data.position
	var tilemap_position: Vector2i = tilemap.local_to_map(world_position)
	# initialize the building instance. We have to manually initialize components that we can get
	# from a this building's stats. After that we require new call to the ready function (it should
	# not be called manually.
	var instance: Building = BUILDING_PREFAB.instantiate()
	instance.set_script( building_data.building_stats.building_script)
	instance.stats = building_data.building_stats
	instance.initialize_components(building_data.component_data)
	instance.request_ready()

	# Each building has to be connected to the bus to know that some other building has been built.
	# This connection should persist when saved on the disk.
	BuildingSignalBus.building_built.connect(instance._on_building_built, ConnectFlags.CONNECT_PERSIST)

	#Take into account building dimensions offset
	# size 2 needs to be moved by 0.5 tile, 3 by 1 tile, 4 by 1.5 tiles etc.
	# therefore it is (0.5 tile) * (dimension - 1)
	var tile_size:Vector2i = tilemap.tile_set.tile_size / 2
	var offset: Vector2i = tile_size * (instance.stats.dimensions - Vector2i.ONE)
	world_position += offset

	instance.position = world_position
	instance.calculate_tiles_covered_by_building(tilemap_position)
	BuildingModeManager.set_tiles_as_occupied(instance)

	for tile in instance.tiles_occupied_by_building:
		shadow_map.set_shadow_at_pos(tile)

	instance.heat_source_data.tile_position = tilemap_position
	instance.heat_source_data.strength = building_data.building_stats.heat_strength
	instance.heat_source_data.radius = building_data.building_stats.heat_radius

	# Finally, add the building a node to the building manager, at this moment, it should be
	# fully initialized.
	if is_inside_tree() == true:
		buildings.add_child(instance)
		instance.owner = buildings

	# Set rotation directly from building_data
	BuildingModeManager.building_rotation = building_data.rotation_degrees
	instance.rotation_degrees = float(building_data.rotation_degrees)
	# Handling animations and syncing them
	BuildingModeManager._sync_animation(instance)

	_building_scaling(instance, building_data)

	if not silent:
		(AudioManager as AudioManagerScript).create_audio(SoundEffect.SOUND_EFFECT_TYPE.BUILDING_PLACED)

	if heat_manager.natural_sources.has(tilemap_position):
		#NOTE second if shouldn't be necessary cause you cant build on already occupied position
		if heat_manager.active_sources.has(heat_manager.natural_sources.get(tilemap_position)):
			heat_manager.modify_natural_heat_source((heat_manager.natural_sources.get(tilemap_position) as HeatSource), false)

	instance.initialize_building_heat_source(heat_manager)
	instance.on_build()

	BuildingSignalBus.building_built.emit.call(instance, is_replacing)

	#if instance.heat_source_data != null:
	#heat_manager.add_building_heat_source(instance.heat_source_data)


func _build_building_at_position(world_position: Vector2i, process_cost: bool = true) -> void:
	var tilemap_position: Vector2i = tilemap.local_to_map(world_position)

	var result: Dictionary = BuildingModeManager.can_build_at_position(tilemap, tilemap_position)
	if not result["can_build"]:
		return
	
	var building_data: BuildingData = BuildingData.new()
	
	if result["reason"] == BuildingModeManager.BuilderStateReason.REPLACEABLE:
		var replaceable_building: Building = BuildingModeManager.get_building_at_position(tilemap_position)

		# Copy important data from old building
		building_data.position = world_position
		building_data.building_stats = BuildingModeManager.selected_building_stats
		building_data.rotation_degrees = _get_building_rotation()

		# Remove old building if desired
		replaceable_building.on_replace_build()
		if replaceable_building.stats.should_free_after_replace:
			demolisher._remove_building(replaceable_building, true)

		# Create new building
		_instantiate_building(building_data, true)
		return

	if process_cost == true:
		var building_cost: Dictionary = BuildingModeManager.selected_building_stats.cost
		
		# TODO Change this so it does not need to be converted into types
		var type_building_cost: Dictionary[ItemType.Enum, int] = {}
		for key: ItemData in building_cost:
			type_building_cost[key.type] = building_cost[key]
		
		if not level_manager.inventory.sufficient_resources(type_building_cost):
			return
		level_manager.inventory.consume_resources(type_building_cost)

	building_data.position = world_position
	building_data.building_stats = BuildingModeManager.selected_building_stats
	building_data.rotation_degrees = _get_building_rotation()

	_instantiate_building(building_data)


func _get_building_rotation() -> int:
	# Returns the correct rotation for the building to be passed to BuildingData
	if not BuildingModeManager.selected_building_stats.is_rotatable:
		return 0

	var building_rotation_degrees: int = BuildingModeManager.building_rotation

	if BuildingModeManager.selected_building_stats.building_type == BuildingType.Enum.CONVEYOR_BELT:
		if start_building_tile != new_mouse_tile:
			building_rotation_degrees = vector_to_degrees(direction_vector)

	return building_rotation_degrees


func vector_to_degrees(direction: Vector2) -> int:
	match direction:
		Vector2(1, 0):
			return 0  # East
		Vector2(0, -1):
			return 270  # North
		Vector2(-1, 0):
			return 180  # West
		Vector2(0, 1):
			return 90  # South
		_:
			push_error("Invalid cardinal direction vector: %s" % direction)
			return 0


func _unhandled_input(event: InputEvent) -> void:
	if StateManager.state == StateManager.States.PLAY:
		return

	if StateManager.state == StateManager.States.DEMOLISH:
		return

	# Do not build anything, when there is nothing to be build.
	if not BuildingModeManager.selected_building_stats:
		return

	if event.is_action_pressed("mouse_lmb"):
		var local_mouse_pos: Vector2 = to_local(get_global_mouse_position())
		start_building_tile = tilemap.local_to_map(local_mouse_pos)
		last_mouse_tile = tilemap.local_to_map(local_mouse_pos)
		is_dragging = true
	elif event.is_action_released("mouse_lmb"):
		_build_building_at_position(tilemap.map_to_local(last_mouse_tile))
		is_dragging = false
	elif event.is_action_pressed("mouse_rmb"):
		_cancel_building()


func _physics_process(_delta: float) -> void:
	if not is_dragging:
		return

	var local_mouse_pos: Vector2 = to_local(get_global_mouse_position())
	new_mouse_tile = tilemap.local_to_map(local_mouse_pos)

	if new_mouse_tile == last_mouse_tile:
		return

	direction_vector = new_mouse_tile - last_mouse_tile

	if new_mouse_tile in _get_cardinal_directions(last_mouse_tile):
		_build_building_at_position(tilemap.map_to_local(last_mouse_tile))
		last_mouse_tile = new_mouse_tile
	else:
		var path: Array[Vector2i] = _get_simple_path(last_mouse_tile, new_mouse_tile)

		#path gets constructed to the last but one tile so 2 different
		#paths could be connected by the default logic
		for tile in range(path.size() - 1):
			last_mouse_tile = path[tile]
			new_mouse_tile = path[tile + 1]
			direction_vector = new_mouse_tile - last_mouse_tile
			_build_building_at_position(tilemap.map_to_local(last_mouse_tile))
			last_mouse_tile = new_mouse_tile


func _get_simple_path(start: Vector2i, end: Vector2i) -> Array[Vector2i]:
	var path: Array[Vector2i] = []
	path.append(start)
	#get for x
	while start.x != end.x:
		start.x += 1 if end.x > start.x else -1
		path.append(start)

	#get for y
	while start.y != end.y:
		start.y += 1 if end.y > start.y else -1
		path.append(start)

	return path


func _get_cardinal_directions(tile_position: Vector2i) -> Array[Vector2i]:
	var tile_array: Array[Vector2i] = []
	for direction: Vector2i in CARDINAL_VECTORS:
		tile_array.append(tile_position + direction)
	return tile_array


func _unhandled_key_input(_event: InputEvent) -> void:
	if StateManager.state == StateManager.States.PLAY:
		return

	if StateManager.state == StateManager.States.DEMOLISH:
		return

	if Input.is_action_just_pressed("rotate_left"):
		BuildingModeManager.building_rotation -= 90
	if Input.is_action_just_pressed("rotate_right"):
		BuildingModeManager.building_rotation += 90
	if Input.is_key_pressed(KEY_DELETE):
		BuildingModeManager.occupied_tiles.clear()
		for node in buildings.get_children():
			node.queue_free()

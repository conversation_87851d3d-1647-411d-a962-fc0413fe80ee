[gd_scene load_steps=5 format=3 uid="uid://cwrd7juoau45u"]

[ext_resource type="Script" uid="uid://b5hmftupw8nkh" path="res://UI/toolbar_item.gd" id="1_ruqkd"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_everp"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_7431h"]
bg_color = Color(0.340339, 0.340339, 0.340339, 0.552941)
border_width_left = 3
border_width_top = 3
border_width_right = 3
border_width_bottom = 3

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_iuowv"]
bg_color = Color(0.0980392, 0.0980392, 0.0980392, 0.552941)
border_width_left = 3
border_width_top = 3
border_width_right = 3
border_width_bottom = 3

[node name="Item1" type="Button"]
custom_minimum_size = Vector2(50, 50)
size_flags_horizontal = 3
size_flags_vertical = 5
theme_override_styles/focus = SubResource("StyleBoxEmpty_everp")
theme_override_styles/hover = SubResource("StyleBoxFlat_7431h")
theme_override_styles/pressed = SubResource("StyleBoxFlat_iuowv")
theme_override_styles/normal = SubResource("StyleBoxFlat_iuowv")
icon_alignment = 1
expand_icon = true
script = ExtResource("1_ruqkd")

[node name="ID" type="Label" parent="."]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -25.0
offset_top = 5.0
offset_right = -6.0
offset_bottom = 23.0
grow_horizontal = 0
theme_override_font_sizes/font_size = 10
text = "0"
horizontal_alignment = 2

[connection signal="mouse_entered" from="." to="." method="_on_mouse_entered"]
[connection signal="mouse_exited" from="." to="." method="_on_mouse_exited"]
[connection signal="pressed" from="." to="." method="_on_pressed"]

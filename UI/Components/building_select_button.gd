class_name BuildingSelectButton
extends Button

var building_stats: BuildingStats
var _is_new: bool:
	set(value):
		_is_new = value

		if not _is_new:
			new_label.hide()
			remove_theme_stylebox_override("normal")

@onready var new_label: Label = $MarginContainer/NewLabel


func initialize_button(building_stat: BuildingStats) -> void:
	self.building_stats = building_stat
	icon = building_stat.texture
	_is_new = SaveManager.get_global_data().get_unlock_manager_data().buildings_new.get_or_add(building_stat, true)


func _on_pressed() -> void:
	##TODO select the building and close the build menu

	BuildingSelectionManager.current_state = BuildingSelectionManager.States.IDLE
	BuildingSelectionManager.manual_building_select.emit()
	BuildingModeManager.selected_building_stats = building_stats
	StateManager.state = StateManager.States.BUILD

	if _is_new:
		SaveManager.get_global_data().get_unlock_manager_data().buildings_new.set(building_stats, false)
		_is_new = false


func _on_mouse_exited() -> void:
	BuildingSelectionManager.current_state = BuildingSelectionManager.States.IDLE
	BuildingSelectionManager.select_hovered.emit(Rect2i(0,0,0,0), null)


func _on_mouse_entered() -> void:
	BuildingSelectionManager.selected_stats = building_stats
	BuildingSelectionManager.current_state = BuildingSelectionManager.States.SELECTING
	StateManager.state = StateManager.States.PLAY
	BuildingSelectionManager.select_hovered.emit(Rect2i(global_position, size), building_stats)

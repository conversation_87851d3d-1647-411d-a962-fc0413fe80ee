class_name MainmenuButton
extends But<PERSON>

const SCALE_FACTOR: float = 0.1
const DEFAULT_TRANSITION_TIME: float = 0.1

var tween: Tween
var start_position: Vector2


func _ready() -> void:
	start_position = global_position


func _on_mouse_entered() -> void:
	if tween and tween.is_running():
		tween.kill()
	tween = create_tween()
	tween.set_parallel()
	start_position = global_position
	tween.tween_property(self, "scale", Vector2(1.0 + SCALE_FACTOR, 1.0 + SCALE_FACTOR), DEFAULT_TRANSITION_TIME)
	tween.tween_property(self, "global_position", start_position - SCALE_FACTOR / 2 * size, DEFAULT_TRANSITION_TIME)


func _on_mouse_exited() -> void:
	if tween and tween.is_running():
		tween.kill()
	tween = create_tween()
	tween.set_parallel()
	tween.tween_property(self, "scale", Vector2(1.0, 1.0), DEFAULT_TRANSITION_TIME)
	tween.tween_property(self, "global_position", start_position, DEFAULT_TRANSITION_TIME)

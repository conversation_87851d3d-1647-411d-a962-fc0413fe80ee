@icon("res://assets/IconGodotNode/control/icon_puzzle.png")

class_name ProductionInfoComponent
extends HBoxContainer

const MAX_ITEMS: int = 4

@export var _in_items: Array[ItemData]
@export var _out_items: Array[ItemData]

var _input_infos: Array[ItemProductionInfo]
var _output_infos: Array[ItemProductionInfo]

var input_item_to_button_map: Dictionary[ItemData, ItemProductionInfo]
var output_item_to_button_map: Dictionary[ItemData, ItemProductionInfo]


var recipe: ItemRecipe = null:
	set(new_recipe):
		clear_for_new_recipe()
		if new_recipe == null:
			return
		for item_data in new_recipe.input_resources:
			add_input_item(item_data, new_recipe.input_resources[item_data])
		for item_data in new_recipe.output_resources:
			add_output_item(item_data, new_recipe.output_resources[item_data])


func _ready() -> void:
	for child in $%InItemsContainer.get_children():
		_input_infos.append(child)
	for child in $%OutItemsContainer.get_children():
		_output_infos.append(child)


func add_input_item(item_data: ItemData, production_quantity: int = 1) -> void:
	if _in_items.size() == MAX_ITEMS:
		push_error("Exceeded maximum number of items")
	
	input_item_to_button_map[item_data] = _input_infos[(input_item_to_button_map.size() - 1) + 1]
	var info: ItemProductionInfo = input_item_to_button_map[item_data]
	info.item_stats = item_data
	info.should_produce_count = production_quantity
	info.show()


func add_output_item(item_data: ItemData, production_quantity: int = 1) -> void:
	if _in_items.size() == MAX_ITEMS:
		push_error("Exceeded maximum number of items")
	
	output_item_to_button_map[item_data] = _output_infos[(output_item_to_button_map.size() - 1) + 1]
	var info: ItemProductionInfo = output_item_to_button_map[item_data]
	info.item_stats = item_data
	info.should_produce_count = production_quantity
	info.show()


func clear_for_new_recipe() -> void:
	input_item_to_button_map.clear()
	output_item_to_button_map.clear()

	for info in _input_infos:
		info.item_stats = null
		info.hide()
	for info in _output_infos:
		info.item_stats = null
		info.hide()
		
	# We want two empty panels to be always visible
	_input_infos.front().show()
	_output_infos.front().show()


func _on_recipe_changed(new_recipe: Recipe) -> void:
	recipe = new_recipe


func _on_held_items_changed(building: Building) -> void:
	var held_items: Dictionary[ItemData, int] = building.get_held_items()
	_update_holding_labels(held_items)


func initialize_with_items(items: Dictionary[ItemData, int]) -> void:
	_update_holding_labels(items)


func _update_holding_labels(items: Dictionary[ItemData, int]) -> void:
	var changed_panels: Array[ItemData]
	
	for item_data in items:
		if item_data in input_item_to_button_map:
			input_item_to_button_map[item_data].holding_count = items[item_data]
			changed_panels.append(item_data)
		if item_data in output_item_to_button_map:
			output_item_to_button_map[item_data].holding_count = items[item_data]
			changed_panels.append(item_data)
			
	for item_data in input_item_to_button_map.keys():
		if item_data not in changed_panels:
			input_item_to_button_map[item_data].holding_count = 0
	
	for item_data in output_item_to_button_map.keys():
		if item_data not in changed_panels:
			output_item_to_button_map[item_data].holding_count = 0

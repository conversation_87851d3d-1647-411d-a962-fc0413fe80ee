[gd_scene load_steps=4 format=3 uid="uid://5grkcttqs2hf"]

[ext_resource type="Texture2D" uid="uid://brro0svjm78l2" path="res://assets/Sprites/32x32/InserterBase.png" id="1_ot6r5"]
[ext_resource type="Script" uid="uid://begmm1l8uvy3f" path="res://UI/Components/building_select_button.gd" id="2_oxaqj"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_7q577"]
content_margin_left = 10.0
content_margin_right = 10.0
bg_color = Color(1, 1, 0, 0.4)
border_width_left = 3
border_width_top = 3
border_width_right = 3
border_width_bottom = 3
corner_radius_top_left = 5
corner_radius_top_right = 5
corner_radius_bottom_right = 5
corner_radius_bottom_left = 5
corner_detail = 1

[node name="BuildingSelectButton" type="Button"]
custom_minimum_size = Vector2(66, 66)
anchors_preset = -1
anchor_right = 0.034375
anchor_bottom = 0.0611111
offset_right = -66.0
offset_bottom = -66.0
theme_override_styles/normal = SubResource("StyleBoxFlat_7q577")
icon = ExtResource("1_ot6r5")
expand_icon = true
script = ExtResource("2_oxaqj")
metadata/_edit_use_anchors_ = true

[node name="MarginContainer" type="MarginContainer" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.242424
anchor_right = 1.0
anchor_bottom = 0.318182
offset_left = 8.0
offset_bottom = -8.0
grow_horizontal = 0
theme_override_constants/margin_left = 5
theme_override_constants/margin_top = 5
theme_override_constants/margin_right = 5
theme_override_constants/margin_bottom = 5
metadata/_edit_use_anchors_ = true

[node name="NewLabel" type="Label" parent="MarginContainer"]
z_index = 4
layout_mode = 2
size_flags_horizontal = 4
theme_override_colors/font_color = Color(1, 1, 0, 1)
theme_override_font_sizes/font_size = 7
text = "New"
horizontal_alignment = 2

[connection signal="mouse_entered" from="." to="." method="_on_mouse_entered"]
[connection signal="mouse_exited" from="." to="." method="_on_mouse_exited"]
[connection signal="pressed" from="." to="." method="_on_pressed"]

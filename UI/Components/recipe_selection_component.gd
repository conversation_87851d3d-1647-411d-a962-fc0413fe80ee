@icon("res://assets/IconGodotNode/control/icon_puzzle.png")

class_name RecipeSelectionComponent
extends OptionButton


signal recipe_changed(new_recipe: Recipe)


var supported_recipes: Array[Recipe] = []


func fill_from_catalogue(building_stats: BuildingStats) -> void:
	# TODO: rename properly
	var complex_recipes: Dictionary[BuildingType.Enum, Array] = UnlockManager.data.unlocked_complex_recipes_catalog
	var recipes = complex_recipes.get(building_stats.building_type)
	if not recipes:
		# TODO: handle no recipes
		return

	for recipe: Recipe in recipes:
		add_icon_item(recipe.recipe_sprite, recipe.recipe_name)
		supported_recipes.append(recipe)


func set_option_to_recipe(recipe: Recipe) -> void:
	var recipe_idx: int = supported_recipes.find(recipe)
	if recipe_idx == -1:
		return
	select(recipe_idx)


func _on_item_selected(index: int) -> void:
	print(supported_recipes)
	print(supported_recipes.size())
	for recipe in supported_recipes:
		print("Recipe: ", recipe)
	recipe_changed.emit(supported_recipes[index])

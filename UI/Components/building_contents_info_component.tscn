[gd_scene load_steps=4 format=3 uid="uid://d2jr6dm76s83m"]

[ext_resource type="Script" path="res://UI/building_contains_info_component.gd" id="1_srlao"]
[ext_resource type="PackedScene" uid="uid://dm618vutss06c" path="res://UI/Components/item_panel.tscn" id="2_017ms"]
[ext_resource type="Script" path="res://UI/Components/item_panel.gd" id="3_017ms"]

[node name="ContainsComponent" type="GridContainer"]
offset_right = 446.0
offset_bottom = 50.0
columns = 2
script = ExtResource("1_srlao")

[node name="ContainsLabel" type="Label" parent="."]
layout_mode = 2
size_flags_horizontal = 2
theme_override_font_sizes/font_size = 12
text = "Contains:"

[node name="HBoxContainer" type="HBoxContainer" parent="."]
custom_minimum_size = Vector2(300, 0)
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 4

[node name="ItemInfoPanel1" parent="HBoxContainer" instance=ExtResource("2_017ms")]
layout_mode = 2
script = ExtResource("3_017ms")

[node name="ItemInfoPanel2" parent="HBoxContainer" instance=ExtResource("2_017ms")]
layout_mode = 2
script = ExtResource("3_017ms")

[node name="ItemInfoPanel3" parent="HBoxContainer" instance=ExtResource("2_017ms")]
layout_mode = 2
script = ExtResource("3_017ms")

[node name="ItemInfoPanel4" parent="HBoxContainer" instance=ExtResource("2_017ms")]
layout_mode = 2
script = ExtResource("3_017ms")

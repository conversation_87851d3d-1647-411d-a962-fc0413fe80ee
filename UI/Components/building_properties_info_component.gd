@icon("res://assets/IconGodotNode/control/icon_puzzle.png")

class_name BuildingPropertiesInfoComponent
extends GridContainer

class Property:
	signal hidden
	var label: Label
	var progress_bar: ProgressBar
	var is_hidden: bool = false
	
	func _init(new_label: Label, new_bar: ProgressBar) -> void:
		label = new_label
		progress_bar = new_bar

	func hide() -> void:
		label.hide()
		progress_bar.hide()
		is_hidden = true
		
	func show() -> void:
		label.show()
		progress_bar.show()
		is_hidden = false


@onready var electricity_property: Property = Property.new(
	%ElectricityLabel,
	%ElectricityProgressBar,
)
@onready var progress_property: Property = Property.new(
	%ProductionProgressLabel,
	%ProductionProgressBar,
)

var progress_timer: Timer = null:
	set(new_timer):
		progress_property.progress_bar.max_value = new_timer.wait_time
		progress_timer = new_timer


func _on_hidden_property() -> void:
	for property: Property in [electricity_property, progress_property]:
		if not property.is_hidden:
			if not visible:
				show()
			return
	hide()


func _ready() -> void:
	electricity_property.hidden.connect(_on_hidden_property)
	progress_property.hidden.connect(_on_hidden_property)


func _on_processing_finished() -> void:
	progress_property.progress_bar.value = 0


func _on_processing_progress_changed(total_progress: float) -> void:
	var bar = progress_property.progress_bar
	bar.value = clamp(total_progress, 0.0, bar.max_value)


func _on_recipe_changed(new_recipe: Recipe) -> void:
	if not new_recipe:
		return
	progress_property.progress_bar.max_value = new_recipe.processing_time
	progress_property.progress_bar.value = 0.0

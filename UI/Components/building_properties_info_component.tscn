[gd_scene load_steps=0 format=3 uid="uid://bm4pop22edmtr"]

[node name="PropertiesContainer" type="GridContainer"]
theme_override_constants/h_separation = 10
columns = 2

[node name="ProductionProgressLabel" type="Label" parent="."]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 0
theme_override_font_sizes/font_size = 12
text = "Progress"

[node name="ProductionProgressBar" type="ProgressBar" parent="."]
unique_name_in_owner = true
custom_minimum_size = Vector2(300, 0)
layout_mode = 2
size_flags_horizontal = 8
size_flags_vertical = 1
show_percentage = false

[node name="ElectricityLabel" type="Label" parent="."]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 0
theme_override_font_sizes/font_size = 12
text = "Electricity"

[node name="ElectricityProgressBar" type="ProgressBar" parent="."]
unique_name_in_owner = true
custom_minimum_size = Vector2(300, 0)
layout_mode = 2
size_flags_horizontal = 8
size_flags_vertical = 3
show_percentage = false

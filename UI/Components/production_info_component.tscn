[gd_scene load_steps=4 format=3 uid="uid://5167j5txqsem"]

[ext_resource type="Script" uid="uid://b0k8qs60rapwf" path="res://UI/Components/production_info_component.gd" id="1_7acp8"]
[ext_resource type="PackedScene" uid="uid://bhunpfl4qmi7c" path="res://UI/Components/item_production_panel.tscn" id="2_uxbpc"]
[ext_resource type="Texture2D" uid="uid://c0pjpa6tt7l80" path="res://assets/Sprites/arrow.png" id="3_lp4co"]

[node name="ProductionInfoComponent" type="HBoxContainer"]
script = ExtResource("1_7acp8")

[node name="InContainer" type="VBoxContainer" parent="."]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 6

[node name="InLabel" type="Label" parent="InContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "In"
horizontal_alignment = 1

[node name="InItemsContainer" type="GridContainer" parent="InContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 6
size_flags_vertical = 4
theme_override_constants/h_separation = 5
theme_override_constants/v_separation = 5
columns = 2

[node name="Button" parent="InContainer/InItemsContainer" instance=ExtResource("2_uxbpc")]
layout_mode = 2

[node name="Button2" parent="InContainer/InItemsContainer" instance=ExtResource("2_uxbpc")]
visible = false
layout_mode = 2

[node name="Button3" parent="InContainer/InItemsContainer" instance=ExtResource("2_uxbpc")]
visible = false
layout_mode = 2

[node name="Button4" parent="InContainer/InItemsContainer" instance=ExtResource("2_uxbpc")]
visible = false
layout_mode = 2

[node name="Arrow" type="TextureRect" parent="."]
layout_mode = 2
size_flags_horizontal = 6
size_flags_vertical = 4
texture = ExtResource("3_lp4co")

[node name="OutContainer" type="VBoxContainer" parent="."]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 6

[node name="OutLabel" type="Label" parent="OutContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "Out"
horizontal_alignment = 1

[node name="OutItemsContainer" type="GridContainer" parent="OutContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 6
theme_override_constants/h_separation = 5
theme_override_constants/v_separation = 5
columns = 2

[node name="Button" parent="OutContainer/OutItemsContainer" instance=ExtResource("2_uxbpc")]
layout_mode = 2

[node name="Button2" parent="OutContainer/OutItemsContainer" instance=ExtResource("2_uxbpc")]
visible = false
layout_mode = 2

[node name="Button3" parent="OutContainer/OutItemsContainer" instance=ExtResource("2_uxbpc")]
visible = false
layout_mode = 2

[node name="Button4" parent="OutContainer/OutItemsContainer" instance=ExtResource("2_uxbpc")]
visible = false
layout_mode = 2

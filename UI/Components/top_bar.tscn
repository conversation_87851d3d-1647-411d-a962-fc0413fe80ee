[gd_scene load_steps=4 format=3 uid="uid://m7kqbvywevag"]

[ext_resource type="Texture2D" uid="uid://cofhysbw4rvl8" path="res://assets/Sprites/32x32/MenuButton.png" id="2_kfs2u"]
[ext_resource type="Texture2D" uid="uid://co0rsd82ol1g4" path="res://assets/Sprites/32x32/InfoButton.png" id="3_0chlr"]
[ext_resource type="PackedScene" uid="uid://6vqqw6p1f3uj" path="res://UI/Components/resource_menu.tscn" id="4_wixi1"]

[node name="TopBar" type="MarginContainer"]
anchors_preset = -1
anchor_right = 1.0
anchor_bottom = 0.065
offset_bottom = -5.2
grow_horizontal = 2
size_flags_horizontal = 3
theme_override_constants/margin_left = 10
theme_override_constants/margin_top = 10
theme_override_constants/margin_right = 10

[node name="HBoxContainer" type="HBoxContainer" parent="."]
layout_mode = 2
mouse_filter = 2

[node name="ControlsMenu" type="HBoxContainer" parent="HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 4
theme_override_constants/separation = 10

[node name="MainMenuButton" type="Button" parent="HBoxContainer/ControlsMenu"]
custom_minimum_size = Vector2(50, 50)
layout_mode = 2
theme_type_variation = &"FilledButton"
icon = ExtResource("2_kfs2u")

[node name="EncyclopediaButton" type="Button" parent="HBoxContainer/ControlsMenu"]
custom_minimum_size = Vector2(50, 50)
layout_mode = 2
theme_type_variation = &"FilledButton"
icon = ExtResource("3_0chlr")

[node name="ResourceMenu" parent="HBoxContainer" instance=ExtResource("4_wixi1")]
layout_mode = 2

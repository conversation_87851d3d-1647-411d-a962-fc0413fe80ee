class_name ResourceMenu
extends HBox<PERSON>ontainer

# TODO: make the ordering of info widgets set. Currently it depends on the
# order from the unlock manager data...
# They could be created beforehand and then showed, but then it wouldn't
# depend only on the item data resource.
var resource_to_info_map: Dictionary[ItemData, BuildingResourceInfo]


func _ready() -> void:
	UnlockManager.data_changed.connect(_on_unlock_data_changed)
	SurfaceSignalBus.resource_amount_changed.connect(_on_resource_amount_changed)
	_on_unlock_data_changed(UnlockManager.data)


func _on_unlock_data_changed(_unlock_data: UnlockManagerData) -> void:
	for item_data: ItemData in _items_from_recipes():
		if not item_data.is_storeable:
			continue
		if resource_to_info_map.get(item_data) == null:
			resource_to_info_map[item_data] = BuildingResourceInfo.INFO_PREFAB.instantiate()
			var info: BuildingResourceInfo = resource_to_info_map[item_data]
			info.item_data = item_data
			if is_inside_tree() == true:
				add_child(info)


func _on_resource_amount_changed(item_type: ItemType.Enum, amount: int) -> void:
	# The level resource manager works only with item types. However, we require the item data,
	# because we also need the sprite. Therefore, we have to search for the type manually.
	for item_data: ItemData in resource_to_info_map.keys():
		if item_data.type == item_type:
			resource_to_info_map[item_data].count = amount


func _items_from_recipes() -> Array[ItemData]:
	var all_items: Array[ItemData] = _items_from_simple_recipes() + _items_from_complex_recipes()
	var seen_items: Array[ItemData]
	all_items.filter(
		func(x: ItemData) -> bool:
			var is_seen: bool = x in seen_items
			if is_seen:
				return false
			seen_items.append(x)
			return true
	)
	return all_items


func _items_from_simple_recipes() -> Array[ItemData]:
	var items: Array[ItemData]
	var unlocked_data: UnlockManagerData = UnlockManager.data
	var catalog: Dictionary = unlocked_data.unlocked_simple_recipes_catalog
	for building: BuildingType.Enum in catalog.keys():
		for item: ItemData in catalog[building]:
			items.append(item)
	return items


func _items_from_complex_recipes() -> Array[ItemData]:
	var items: Array[ItemData]
	var unlocked_data: UnlockManagerData = UnlockManager.data
	var catalog: Dictionary = unlocked_data.unlocked_complex_recipes_catalog
	for building: BuildingType.Enum in catalog.keys():
		for item_recipe: ItemRecipe in catalog[building]:
			for item: ItemData in item_recipe.input_resources.keys():
				if item not in items:
					items.append(item)
			for item: ItemData in item_recipe.output_resources.keys():
				if item not in items:
					items.append(item)
	return items

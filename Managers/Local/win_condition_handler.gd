class_name WinConditionHandler
extends Node

@export var win_condition_data: Array[WinConditionData] = []

var status_labels: Array[Label] = []
var condition_results: Array[bool] = [false, false, false]

var _win_conditions: Array[WinCondition] = []
var _can_win: bool = false

@onready var status_visuals: Array[Control] = [
	$%Objective1,
	$%Objective2,
	$%Objective3,
]

@onready var hide_button: Button = $%HideButton
@onready var show_button: Button = $%ShowButton
@onready var parent_node: Control = $WinStatusCanvas/Control
@onready var panel_container: PanelContainer = $%PanelContainer
@onready var finish_prompt: HBoxContainer = $%FinishPrompt


func _ready() -> void:
	for data in win_condition_data:
		var script: GDScript = WinConditions.get_condition_script(data.win_condition_type)
		assert(script != null)

		var node := script.new() as WinCondition
		node.data = data.duplicate()

		if is_inside_tree() == true:
			add_child(node)

		_win_conditions.append(node)
	BuildingSignalBus.win_condition_updated.connect(_check_conditions)
	_update_text()
	_check_conditions()


func _input(event: InputEvent) -> void:
	if event.is_action_pressed(&"instant_win"):
		BuildingSignalBus.player_won.emit()
	if _can_win and event.is_action_pressed("finish_level"):
		BuildingSignalBus.player_won.emit()


func _check_conditions() -> void:
	for idx in range(len(_win_conditions)):
		condition_results[idx] = _win_conditions[idx].check_condition()
	_can_win = condition_results[0]
	_update_status()


func _update_text() -> void:
	for idx in range(len(_win_conditions)):
		var text_label: Label = status_visuals[idx].get_child(0)
		status_labels.append(status_visuals[idx].get_child(1))
		text_label.text = _win_conditions[idx].condition_to_string()
		text_label.add_theme_color_override("font_color", Color.WHITE)


func _update_status() -> void:
	if _can_win:
		finish_prompt.show()
	else:
		finish_prompt.hide()

	var not_blocked: bool = true
	for idx in range(len(_win_conditions)):
		status_labels[idx].text = _win_conditions[idx].status_to_string()

		var condition: bool = condition_results[idx]

		if condition and not_blocked:
			status_labels[idx].add_theme_color_override("font_color", Color.GREEN)
		elif condition and not not_blocked:
			status_labels[idx].add_theme_color_override("font_color", Color.YELLOW)
		else:
			status_labels[idx].add_theme_color_override("font_color", Color.RED)

		if not condition:
			not_blocked = false


func _on_hide_button_pressed() -> void:
	hide_button.hide()
	show_button.show()
	# manual animation to deal with changing resolution
	var tween: Tween = create_tween()
	tween.set_loops(1)
	tween.tween_property(parent_node, "position", parent_node.position + Vector2(panel_container.size.x, 0), 0.1)


func _on_show_button_pressed() -> void:
	hide_button.show()
	show_button.hide()
	# manual animation to deal with changing resolution
	var tween: Tween = create_tween()
	tween.set_loops(1)
	tween.tween_property(parent_node, "position", parent_node.position - Vector2(panel_container.size.x, 0), 0.1)

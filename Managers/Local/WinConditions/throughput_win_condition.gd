class_name ThroughputWinCondition
extends WinCondition

var _throughputs: Dictionary[int, int] = {}


func _ready() -> void:
	BuildingSignalBus.sink_consumed.connect(_update_data)


func check_condition() -> bool:
	return data.status >= data.target_amount


func _update_data(item_consumed: ItemType.Enum, throughput: int, sink_id: int) -> void:
	if item_consumed == data.target_item:
		_throughputs[sink_id] = throughput
		var sum: int = 0
		for each: int in _throughputs.values():
			sum += each
		data.status = sum
		BuildingSignalBus.win_condition_updated.emit()


func condition_to_string() -> String:
	return "Have a throughput of %d %ss per minute" % [data.target_amount, ItemType.enum_to_string(data.target_item)]


func status_to_string() -> String:
	return "%d / %d" % [data.status, data.target_amount]

class_name BuildLimitWinCondition
extends WinCondition


func _ready() -> void:
	BuildingSignalBus.building_built.connect(_possible_increase)
	BuildingSignalBus.building_demolished.connect(_possible_decrease)


func check_condition() -> bool:
	return data.status <= data.target_amount


func _possible_increase(building: Building, _is_replacing: bool) -> void:
	if building.stats.building_type == data.target_building:
		data.status += 1
		BuildingSignalBus.win_condition_updated.emit()


func _possible_decrease(building: Building) -> void:
	if building.stats.building_type == data.target_building:
		data.status -= 1
		BuildingSignalBus.win_condition_updated.emit()


func condition_to_string() -> String:
	return "Build at most %d %ss" % [data.target_amount, BuildingType.enum_to_string(data.target_building)]


func status_to_string() -> String:
	return "%d / %d" % [data.status, data.target_amount]

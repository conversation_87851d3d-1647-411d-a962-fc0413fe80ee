[gd_scene load_steps=16 format=3 uid="uid://blsld3nyl1ao6"]

[ext_resource type="Script" uid="uid://cbh012ulqfipa" path="res://Managers/Local/win_condition_handler.gd" id="1_7qulm"]
[ext_resource type="Script" uid="uid://dj4gow3614evw" path="res://Managers/Local/WinConditions/win_condition_data.gd" id="2_f7y83"]
[ext_resource type="Texture2D" uid="uid://d3be16wdn8py8" path="res://assets/Text/Arrow_Right.png" id="4_8yt7q"]
[ext_resource type="Texture2D" uid="uid://dbx1pjh77s0gg" path="res://assets/Text/Arrow_Left.png" id="5_16kyo"]

[sub_resource type="Resource" id="Resource_helbh"]
script = ExtResource("2_f7y83")
target_item = 7
target_building = 0
target_amount = 10
win_condition_type = 0
status = 0
metadata/_custom_type_script = "uid://dj4gow3614evw"

[sub_resource type="Resource" id="Resource_ltxql"]
script = ExtResource("2_f7y83")
target_item = 0
target_building = 1
target_amount = 25
win_condition_type = 1
status = 0
metadata/_custom_type_script = "uid://dj4gow3614evw"

[sub_resource type="Resource" id="Resource_tcvdp"]
script = ExtResource("2_f7y83")
target_item = 1
target_building = 0
target_amount = 30
win_condition_type = 2
status = 0
metadata/_custom_type_script = "uid://dj4gow3614evw"

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_7qulm"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_f7y83"]
bg_color = Color(0.377919, 0.377919, 0.377919, 0.588235)
border_width_right = 2
corner_radius_top_left = 10
corner_radius_bottom_left = 10

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_km6cb"]
bg_color = Color(0.601223, 0.601223, 0.601223, 0.588235)
border_width_right = 2
corner_radius_top_left = 10
corner_radius_bottom_left = 10

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_of4bx"]
bg_color = Color(0.114897, 0.114897, 0.114897, 0.588235)
border_width_right = 2
corner_radius_top_left = 10
corner_radius_bottom_left = 10

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_xscsy"]
bg_color = Color(0.114897, 0.114897, 0.114897, 0.588235)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_ld4c6"]

[sub_resource type="FontFile" id="FontFile_ld4c6"]
subpixel_positioning = 0
msdf_pixel_range = 14
msdf_size = 128
cache/0/16/0/ascent = 0.0
cache/0/16/0/descent = 0.0
cache/0/16/0/underline_position = 0.0
cache/0/16/0/underline_thickness = 0.0
cache/0/16/0/scale = 1.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_7qulm"]
bg_color = Color(0.877616, 0.877616, 0.877616, 1)
border_width_left = 3
border_width_top = 1
border_width_right = 3
border_width_bottom = 3
border_color = Color(0.462291, 0.462291, 0.46229, 1)
corner_radius_top_left = 5
corner_radius_top_right = 5
corner_radius_bottom_right = 5
corner_radius_bottom_left = 5

[node name="WinConditionHandler" type="Node2D"]
script = ExtResource("1_7qulm")
win_condition_data = Array[ExtResource("2_f7y83")]([SubResource("Resource_helbh"), SubResource("Resource_ltxql"), SubResource("Resource_tcvdp")])

[node name="WinStatusCanvas" type="CanvasLayer" parent="."]

[node name="Control" type="Control" parent="WinStatusCanvas"]
layout_mode = 3
anchor_left = 0.815
anchor_right = 1.0
anchor_bottom = 0.23
offset_left = 355.2
offset_bottom = -248.4
grow_horizontal = 0
metadata/_edit_use_anchors_ = true

[node name="VBoxContainer" type="VBoxContainer" parent="WinStatusCanvas/Control"]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -357.0
offset_top = 71.0
offset_bottom = 297.0
grow_horizontal = 0

[node name="HBoxContainer" type="HBoxContainer" parent="WinStatusCanvas/Control/VBoxContainer"]
layout_mode = 2
theme_override_constants/separation = 0
metadata/_edit_use_anchors_ = true

[node name="HideButton" type="Button" parent="WinStatusCanvas/Control/VBoxContainer/HBoxContainer"]
unique_name_in_owner = true
custom_minimum_size = Vector2(30, 0)
layout_mode = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_7qulm")
theme_override_styles/hover = SubResource("StyleBoxFlat_f7y83")
theme_override_styles/pressed = SubResource("StyleBoxFlat_km6cb")
theme_override_styles/normal = SubResource("StyleBoxFlat_of4bx")
icon = ExtResource("4_8yt7q")
expand_icon = true

[node name="ShowButton" type="Button" parent="WinStatusCanvas/Control/VBoxContainer/HBoxContainer"]
unique_name_in_owner = true
visible = false
custom_minimum_size = Vector2(30, 0)
layout_mode = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_7qulm")
theme_override_styles/hover = SubResource("StyleBoxFlat_f7y83")
theme_override_styles/pressed = SubResource("StyleBoxFlat_km6cb")
theme_override_styles/normal = SubResource("StyleBoxFlat_of4bx")
icon = ExtResource("5_16kyo")
expand_icon = true

[node name="PanelContainer" type="PanelContainer" parent="WinStatusCanvas/Control/VBoxContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_xscsy")

[node name="MarginContainer" type="MarginContainer" parent="WinStatusCanvas/Control/VBoxContainer/HBoxContainer/PanelContainer"]
layout_mode = 2
theme_override_constants/margin_left = 12
theme_override_constants/margin_bottom = 12

[node name="WinConditionStatus" type="VBoxContainer" parent="WinStatusCanvas/Control/VBoxContainer/HBoxContainer/PanelContainer/MarginContainer"]
clip_contents = true
layout_mode = 2
size_flags_horizontal = 6
size_flags_vertical = 4

[node name="ObjectivesLabel" type="Label" parent="WinStatusCanvas/Control/VBoxContainer/HBoxContainer/PanelContainer/MarginContainer/WinConditionStatus"]
layout_mode = 2
text = "Objectives"
horizontal_alignment = 1

[node name="Objective1" type="HBoxContainer" parent="WinStatusCanvas/Control/VBoxContainer/HBoxContainer/PanelContainer/MarginContainer/WinConditionStatus"]
unique_name_in_owner = true
layout_mode = 2

[node name="Task" type="Label" parent="WinStatusCanvas/Control/VBoxContainer/HBoxContainer/PanelContainer/MarginContainer/WinConditionStatus/Objective1"]
custom_minimum_size = Vector2(200, 0)
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 1
theme_override_colors/font_color = Color(0, 1, 0, 1)
theme_override_font_sizes/font_size = 10
text = "Generate X of X within 1 minute"
vertical_alignment = 1
autowrap_mode = 2

[node name="Status" type="Label" parent="WinStatusCanvas/Control/VBoxContainer/HBoxContainer/PanelContainer/MarginContainer/WinConditionStatus/Objective1"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
size_flags_horizontal = 3
size_flags_stretch_ratio = 0.5
theme_override_colors/font_color = Color(0, 1, 0, 1)
theme_override_font_sizes/font_size = 10
text = "(131/42)"
horizontal_alignment = 1
autowrap_mode = 2

[node name="Divider" type="Panel" parent="WinStatusCanvas/Control/VBoxContainer/HBoxContainer/PanelContainer/MarginContainer/WinConditionStatus"]
custom_minimum_size = Vector2(300, 1)
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 4
theme_override_styles/panel = SubResource("StyleBoxFlat_ld4c6")

[node name="Objective2" type="HBoxContainer" parent="WinStatusCanvas/Control/VBoxContainer/HBoxContainer/PanelContainer/MarginContainer/WinConditionStatus"]
unique_name_in_owner = true
layout_mode = 2

[node name="Task" type="Label" parent="WinStatusCanvas/Control/VBoxContainer/HBoxContainer/PanelContainer/MarginContainer/WinConditionStatus/Objective2"]
custom_minimum_size = Vector2(200, 0)
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 1
theme_override_colors/font_color = Color(1, 0, 0, 1)
theme_override_font_sizes/font_size = 10
text = "Generate Y of Y within 1 minute"
vertical_alignment = 1
autowrap_mode = 2

[node name="Status" type="Label" parent="WinStatusCanvas/Control/VBoxContainer/HBoxContainer/PanelContainer/MarginContainer/WinConditionStatus/Objective2"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
size_flags_horizontal = 3
size_flags_stretch_ratio = 0.5
theme_override_colors/font_color = Color(1, 0, 0, 1)
theme_override_font_sizes/font_size = 10
text = "(0/13)"
horizontal_alignment = 1
autowrap_mode = 2

[node name="Divider2" type="Panel" parent="WinStatusCanvas/Control/VBoxContainer/HBoxContainer/PanelContainer/MarginContainer/WinConditionStatus"]
custom_minimum_size = Vector2(300, 1)
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 4
theme_override_styles/panel = SubResource("StyleBoxFlat_ld4c6")

[node name="Objective3" type="HBoxContainer" parent="WinStatusCanvas/Control/VBoxContainer/HBoxContainer/PanelContainer/MarginContainer/WinConditionStatus"]
unique_name_in_owner = true
layout_mode = 2

[node name="Task" type="Label" parent="WinStatusCanvas/Control/VBoxContainer/HBoxContainer/PanelContainer/MarginContainer/WinConditionStatus/Objective3"]
custom_minimum_size = Vector2(200, 0)
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 1
theme_override_colors/font_color = Color(1, 0, 0, 1)
theme_override_font_sizes/font_size = 10
text = "Don't use any Z"
vertical_alignment = 1
autowrap_mode = 2

[node name="Status" type="Label" parent="WinStatusCanvas/Control/VBoxContainer/HBoxContainer/PanelContainer/MarginContainer/WinConditionStatus/Objective3"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
size_flags_horizontal = 3
size_flags_stretch_ratio = 0.5
theme_override_colors/font_color = Color(1, 0, 0, 1)
theme_override_font_sizes/font_size = 10
text = "(Fail)"
horizontal_alignment = 1
autowrap_mode = 2

[node name="FinishPrompt" type="HBoxContainer" parent="WinStatusCanvas/Control/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 8
theme_override_constants/separation = 5

[node name="Press" type="Label" parent="WinStatusCanvas/Control/VBoxContainer/FinishPrompt"]
layout_mode = 2
theme_override_font_sizes/font_size = 15
text = "Press"

[node name="Button" type="Label" parent="WinStatusCanvas/Control/VBoxContainer/FinishPrompt"]
custom_minimum_size = Vector2(30, 30)
layout_mode = 2
theme_override_colors/font_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 0
theme_override_fonts/font = SubResource("FontFile_ld4c6")
theme_override_styles/normal = SubResource("StyleBoxFlat_7qulm")
text = "F"
horizontal_alignment = 1
vertical_alignment = 1

[node name="To finish" type="Label" parent="WinStatusCanvas/Control/VBoxContainer/FinishPrompt"]
layout_mode = 2
theme_override_font_sizes/font_size = 15
text = "to finish"

[connection signal="pressed" from="WinStatusCanvas/Control/VBoxContainer/HBoxContainer/HideButton" to="." method="_on_hide_button_pressed"]
[connection signal="pressed" from="WinStatusCanvas/Control/VBoxContainer/HBoxContainer/ShowButton" to="." method="_on_show_button_pressed"]

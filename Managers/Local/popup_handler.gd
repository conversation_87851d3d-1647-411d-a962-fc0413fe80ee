class_name PopupHandler
extends Control

@onready var _building_popup: PopupPanel = %BuildingPopup
@onready var _building_name: Label = %Name
@onready var _building_description: Label = %Description
@onready var _building_costs: Array[Cost] = [
	%Cost1,
	%Cost2,
	%Cost3,
	%Cost4,
]


func building_popup(position_rect: Rect2i, building: BuildingStats) -> void:
	_building_name.text = building.resource_name
	_building_description.text = building.description

	var keys: Array[ItemData] = building.cost.keys()
	for idx in _building_costs.size():
		if idx < keys.size():
			_building_costs[idx].show()
			_building_costs[idx].set_cost(keys[idx], int(building.cost.get(keys[idx])))
		else:
			_building_costs[idx].hide()

	# Reset so it can shrink if needed, then query the size it *wants*
	# (reset_size() clears the cached size to the minimum dictated by children)
	_building_popup.reset_size()
	var desired_size: Vector2i = _building_popup.size

	# 4) Compute correction based on the *desired* width, not the stale size
	var mouse_pos: Vector2 = get_viewport().get_mouse_position()
	var view_port_width: float = get_viewport_rect().size.x
	var correction: Vector2i = Vector2i(position_rect.size.x, 0) if mouse_pos.x <= view_port_width / 2.0 else -Vector2i(desired_size.x, 0)

	# 5) Finally, popup at the rect using the recomputed desired size
	_building_popup.popup(Rect2i(position_rect.position + correction, desired_size))


func hide_building_popup() -> void:
	_building_popup.hide()

class_name HeatHandler
extends Node2D

@export var max_heat: int = 30  # full alpha at this heat value

var atlas_id: int = 0
var tile_id: Vector2i = Vector2i(0, 0)
var tile_size: int = 32

var active_sources: Array[HeatSource]
var inactive_sources: Array[HeatSource]
var natural_sources: Dictionary[Vector2i,HeatSource]
var building_sources: Array[HeatSource]
var heat_map: Dictionary[Vector2i,int] = {}

@onready var heat_sources_tilemap: TileMapLayer = %Tiles/HeatSourceMap
@onready var heat_overlay: MultiMeshInstance2D = %Tiles/HeatOverlay


func _ready() -> void:
	_get_natural_heat_sources()
	_filter_active_natural_heat_sources()
	initialize_natural_heat_map()
	update_heat_overlay()


##Fills Dictionary natural_sources with natural heat sources based on the tiles in HeatSourceMap
##creating new HeatSource class for each of them based on custom tile data
func _get_natural_heat_sources() -> void:
	natural_sources.clear()
	var sources: Array[Vector2i] = heat_sources_tilemap.get_used_cells()
	for source: Vector2i in sources:
		var heat_source: HeatSource = HeatSource.new()
		var heat: int = heat_sources_tilemap.get_cell_tile_data(source).get_custom_data("Heat")
		var heat_radius: int = heat_sources_tilemap.get_cell_tile_data(source).get_custom_data("Heat Radius")

		heat_source.tile_position = source
		heat_source.radius = heat_radius
		heat_source.strength = heat
		natural_sources[source] = heat_source


##Recalculates the heat map for particular heat source based on if it's been activated or deactivated
func modify_natural_heat_source(source: HeatSource, add: bool) -> void:
	LogManager.debug("modifying source at position %s" % source.tile_position, self)

	var radius: int = source.radius
	var heat: int = source.strength
	for x in range(-radius, radius + 1):
		for y in range(-radius, radius + 1):
			var dist: float = sqrt(x * x + y * y)
			if float(dist) <= radius:
				var pos: Vector2i = source.tile_position + Vector2i(x, y)

				var falloff: float = 1.0 - (dist / radius)
				#LogManager.debug('falloff  %s' % falloff, self)
				var change: int = int(heat * falloff)
				#LogManager.debug('change %s' % change, self)
				if change > 0:
					if add:
						heat_map[pos] = heat_map.get(pos, 0) + change
						active_sources.append(source)
						inactive_sources.erase(source)
					else:
						heat_map[pos] = heat_map.get(pos, 0) - change
						inactive_sources.append(source)
						active_sources.erase(source)
	update_heat_overlay()


##Sets up active_sources Array based on heat sources in natural_sources Dictionary
func _filter_active_natural_heat_sources() -> void:
	active_sources.clear()
	for source: HeatSource in natural_sources.values():
		active_sources.append(source)


##Fills the empty heat map with initial values based on naual heat sources
func initialize_natural_heat_map() -> void:
	LogManager.debug("Initializing heat map", self)

	heat_map.clear()
	for source: HeatSource in active_sources:
		#LogManager.log("heat source  position %s, strength %s, radius %s" %\
		# [source.tile_position, source.strength, source.radius], LogManager.LogLevel.DEBUG,self)
		var radius: int = source.radius
		var heat: int = source.strength
		for x in range(-radius, radius + 1):
			for y in range(-radius, radius + 1):
				var dist: float = sqrt(x * x + y * y)
				if dist <= float(radius):
					var pos: Vector2i = source.tile_position + Vector2i(x, y)

					var falloff: float = 1.0 - (dist / radius)
					#LogManager.debug('falloff  %s' % falloff, self)
					var change: int = int(heat * falloff)
					#LogManager.debug('change %s' % change, self)
					if change > 0:
						heat_map[pos] = heat_map.get(pos, 0) + change
	#LogManager.log('heatmap \n %s' %heat_map, LogManager.LogLevel.DEBUG,self)


func heat_overlay_toggle() -> void:
	heat_overlay.visible = !heat_overlay.visible


##recalculates the multimesh to reflect the changes in the heatmap
func update_heat_overlay() -> void:
	var heat_cells: Array[Vector2i] = heat_map.keys()
	var mm: MultiMesh = MultiMesh.new()
	mm.transform_format = MultiMesh.TRANSFORM_2D
	mm.use_colors = true
	mm.instance_count = heat_cells.size()
	mm.visible_instance_count = heat_cells.size()
	
	var quad_mesh: QuadMesh = QuadMesh.new()
	quad_mesh.size = Vector2(tile_size, tile_size)
	mm.mesh = quad_mesh

	var i: int = 0
	for cell: Vector2i in heat_cells:
		var heat: int = heat_map[cell]
		var alpha: float

		#need to offset the position by half tile since the quad mest uses world 0,0 for origin
		var xform: Transform2D = Transform2D.IDENTITY
		xform.origin = Vector2(cell) * tile_size + Vector2(tile_size, tile_size) * 0.5

		var tile_color: Color

		if heat > 0:
			alpha = clamp(float(heat) / max_heat, 0.0, 1.0)
			tile_color = Color(1, 0, 0, alpha * 0.5)  # Red with alpha
		elif heat < 0:
			alpha = clamp(abs(float(heat)) / max_heat, 0.0, 1.0)
			tile_color = Color(0, 0, 1, alpha * 0.5)  # Blue with alpha
		else:
			tile_color = Color(0, 0, 0, 0)
		mm.set_instance_transform_2d(i, xform)
		mm.set_instance_color(i, tile_color)
		i += 1
	heat_overlay.multimesh = mm


func _input(event: InputEvent) -> void:
	if event.is_action_pressed("heat_overlay_toggle"):
		heat_overlay_toggle()

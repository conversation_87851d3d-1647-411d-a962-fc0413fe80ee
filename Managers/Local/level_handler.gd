class_name LevelHandler
extends Node2D

@export var limiter_multiplier: float = 5.0

var start_dialog: Resource = load("res://Entities/Dialogs/asistent/game_start.dtl")
var tutorial_dialog: Resource = load("res://Entities/Dialogs/tutorial_timeline.dtl")
# Inventory is now stored in planet_data, not as a scene node
var inventory: ResourceInventory

const TIME_FOR_RESOURCE: float = 5.0
var _current_time: float = 0.0

@onready var ground_layer: TileMapLayer = %Tiles/Ground
@onready var camera: SpaceCamera = $Camera2D

## Calculates the position of midpoint, i.e. the middle of playable area.
func get_midpoint_of_map() -> Vector2:
	var used_rect: Rect2i = ground_layer.get_used_rect()
	var used_rect_in_world: Rect2 = Rect2(
		used_rect.position * ground_layer.tile_set.tile_size,
		used_rect.size * ground_layer.tile_set.tile_size,
	)
	var midpoint: Vector2 = used_rect_in_world.get_center()
	return midpoint


func _ready() -> void:
	inventory = SaveManager.get_planet_data().get_inventory()
	for item in ItemType.get_storable_items():
		if item not in inventory.resources:
			inventory.resources[item] = 0

	if Dialogic.current_timeline != null:
		return

	Dialogic.signal_event.connect(_on_dialogic_signal)

	Dialogic.start(start_dialog)
	get_viewport().set_input_as_handled()

	set_map_scale()

	StateManager.state = StateManager.States.PLAY


func set_map_scale() -> void:
	# TODO: although we have a method for getting the midpoint, the calculation of
	# current rectangle in pixels should be part of some tiles related library.
	var used_rect: Rect2i = ground_layer.get_used_rect()
	var used_rect_in_world: Rect2 = Rect2(
		used_rect.position * ground_layer.tile_set.tile_size,
		used_rect.size * ground_layer.tile_set.tile_size,
	)
	var midpoint: Vector2 = used_rect_in_world.get_center()

	var background: SpaceBackground = $CameraLimiter/Background
	if not background:
		return
	background.size = used_rect_in_world.size * limiter_multiplier
	background.resolution = background.size

	var limiter: ReferenceRect = $CameraLimiter
	limiter.size = background.size
	limiter.global_position = midpoint - (limiter.size / 2)
	background.position = Vector2(background.size / 2)

	camera.center_camera_on(midpoint)



func _on_dialogic_signal(argument: String) -> void:
	if argument == "start_tutorial":
		Dialogic.start(tutorial_dialog)


func _process(delta: float) -> void:
	_current_time += delta
	if _current_time > TIME_FOR_RESOURCE:
		_current_time = 0.0
		inventory.add_resource(1, ItemType.Enum.IRON_INGOT)

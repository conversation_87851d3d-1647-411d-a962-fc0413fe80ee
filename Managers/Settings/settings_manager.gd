extends Node

enum Settings {
	# General
	# Audio
	MASTER_VOLUME,
	MUSIC_VOLUME,
	SOUND_VOLUME,
	# Video
	WINDOW_MODE,
	RESOLUTION,
}

enum WindowModes {
	WINDOWED,
	FULL<PERSON><PERSON><PERSON>,
	BORDERLESS_FULLSCREEN,
}

enum Resolutions {
	TWO_K,
	FULL_HD,
	HD,
}

# General
# Audio
## Master volume controls the volume as a whole
var master_volume: float = 100.0
## Music volume controls the volume of only music
var music_volume: float = 100.0
## Sound volume controls the volume of sound effects
var sound_volume: float = 100.0
# Video
## Window mode controls how the game is displayed
var window_mode: WindowModes = WindowModes.WINDOWED:
	get:
		return window_mode
	set(new_value):
		var window: Window = get_window()
		match new_value:
			WindowModes.WINDOWED:
				window.set_mode(Window.MODE_WINDOWED)
			WindowModes.FULLSCREEN:
				window.set_mode(Window.MODE_EXCLUSIVE_FULLSCREEN)
			WindowModes.BORDERLESS_FULLSCREEN:
				window.set_mode(Window.MODE_FULLSCREEN)
			_:
				LogManager.error("Tried to set invalid window mode", self)
				return
		window_mode = new_value
		resolution = resolution

## Resolution controls the size of the screen
var resolution: Resolutions = Resolutions.FULL_HD:
	get:
		return resolution
	set(new_value):
		var window: Window = get_window()
		var new_size: Vector2i
		match new_value:
			Resolutions.TWO_K:
				new_size = Vector2i(2560, 1440)
			Resolutions.FULL_HD:
				new_size = Vector2i(1920, 1080)
			Resolutions.HD:
				new_size = Vector2i(1280, 720)
			_:
				LogManager.error("Tried to set invalid resolution", self)
				return

		if window.mode == Window.MODE_EXCLUSIVE_FULLSCREEN or window.mode == Window.MODE_FULLSCREEN:
			window.content_scale_size = new_size
		else:
			window.size = new_size

		resolution = new_value


func set_float_settings(setting: Settings, new_value: float) -> void:
	match setting:
		Settings.MASTER_VOLUME:
			master_volume = new_value
		Settings.MUSIC_VOLUME:
			music_volume = new_value
		Settings.SOUND_VOLUME:
			sound_volume = new_value
		_:
			LogManager.error("Tried to set setting which does not support floats", self)


func set_enum_settings(setting: Settings, new_idx: int) -> void:
	match setting:
		Settings.WINDOW_MODE:
			window_mode = WindowModes.values()[new_idx]
		Settings.RESOLUTION:
			resolution = Resolutions.values()[new_idx]
		_:
			LogManager.error("Tried to set setting which does not support ints", self)

#
#func get_setting_value(setting: Settings) -> Variant:
	#match setting:
		#Settings.MASTER_VOLUME:
			#return master_volume
		#Settings.MUSIC_VOLUME:
			#return music_volume
		#Settings.SOUND_VOLUME:
			#return sound_volume
		#Settings.WINDOW_MODE:
			#return window_mode
		#Settings.RESOLUTION:
			#return resolution
		#_:
			#LogManager.error("Tried to get invalid setting", self)
			#return -1


func get_setting_value(setting: Settings) -> int:
	match setting:
		Settings.WINDOW_MODE:
			return window_mode
		Settings.RESOLUTION:
			return resolution
		_:
			LogManager.error("Setting is not enum-based", self)
			return -1
			
			
func get_setting_value_float(setting: Settings) -> float:
	match setting:
		Settings.MASTER_VOLUME:
			return master_volume
		Settings.MUSIC_VOLUME:
			return music_volume
		Settings.SOUND_VOLUME:
			return sound_volume
		_:
			LogManager.error("Setting is not float-based", self)
			return -1.0

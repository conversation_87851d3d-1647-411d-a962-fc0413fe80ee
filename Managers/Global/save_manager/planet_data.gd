class_name PlanetData
extends Resource

## List of win conditions not the final ones
## These are used for their data to rebuild actuall win conditions
@export var win_conditions: Array[WinConditionData]

# Stores all building data for the planet
@export var buildings_data: Dictionary[Vector2i,BuildingData]
# Stores the inventory state for the planet
@export var inventory: ResourceInventory
# Stores the toolbar data for the planet
@export var toolbar_data: Dictionary[int, BuildingStats]


# Get or create building datas
func get_buildings_data() -> Dictionary[Vector2i,BuildingData]:
	if not buildings_data:
		buildings_data = {}
	return buildings_data


# Get or create inventory
func get_inventory() -> ResourceInventory:
	if not inventory:
		inventory = ResourceInventory.new()
		call_deferred("get_initial_recources")
	return inventory


func get_initial_recources() -> void:
	inventory.add_resource(5,ItemType.Enum.IRON_INGOT)
	inventory.add_resource(20,ItemType.Enum.COPPER_INGOT)
	

# Get toolbar data
func get_toolbar_data() -> Dictionary[int, BuildingStats]:
	if not toolbar_data:
		toolbar_data = {}
	return toolbar_data

@tool
extends Node2D

## Array of all buildings
var buildings: Array[BuildingStats]


func _ready() -> void:
	# TODO may be some lazy loading in future would be nice
	_get_all_buildings("res://Entities/Buildings/")


## Loads all buildings from folder to array
func _get_all_buildings(path: String) -> void:
	var file_paths: Array[String] = ResourceFinder.find(path, ".tres")

	for file_path in file_paths:
		var res: Resource = ResourceLoader.load(file_path)
		if res != null and res is BuildingStats:
			buildings.push_back(res)


func print(buildings_to_print: Array[BuildingStats]) -> void:
	for building: BuildingStats in buildings_to_print:
		var building_name: String = building.resource_name
		assert(not building_name.is_empty())
		var line: String = "\t" + building_name.rpad(10)
		LogManager.debug(line, self)

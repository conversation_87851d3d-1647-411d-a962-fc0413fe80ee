extends Node

signal power_debug_changed(new_value: bool)
signal item_debug_changed(new_value: bool)

## Cause network to announce zero power recieved/provided
## Also shows all network infos
var power_debug: bool = false
var item_debug: bool = false


func _input(event: InputEvent) -> void:
	if event.is_action_pressed(&"debug_power"):
		power_debug = not power_debug
		power_debug_changed.emit(power_debug)
		get_viewport().set_input_as_handled()

	if event.is_action_pressed(&"item_debug"):
		item_debug = not item_debug
		item_debug_changed.emit(item_debug)
		get_viewport().set_input_as_handled()

	if event.is_action_pressed(&"debug_speed"):
		if Engine.time_scale < 1:
			return
		
		Engine.time_scale = 20	
		get_viewport().set_input_as_handled()
	
	if event.is_action_released(&"debug_speed"):
		if Engine.time_scale < 1:
			return
		
		Engine.time_scale = 1
		get_viewport().set_input_as_handled()
	
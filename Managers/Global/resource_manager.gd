# _ready is called only on nodes, therefore, it cannot be a RefCounted
extends Node

## Path to a directory where expect the item data resources to be in.
const ITEMS_PATH = "res://Entities/Items/Resources/"

## A mapping between string names in upper case to item data classes. It is dynamically created
## at the start of game runtime. Used to remap string name back to item (used by mines to get items)
var string_name_to_item_data_map: Dictionary[StringName, ItemData]


func _ready() -> void:
	var file_paths: Array[String] = ResourceFinder.find(ITEMS_PATH, &".tres")

	for file_path in file_paths:
		var res: Resource = ResourceLoader.load(file_path)
		if res != null and res is ItemData:
			var item: ItemData = res as ItemData
			var item_name: StringName = item.resource_path.get_file().split(".")[0].to_upper()
			string_name_to_item_data_map[item_name] = item

			assert(res.resource_name)

extends Node

## Represents the reason behind the builder state.
# It is cheaper to compare enums than to compare strings.
enum BuilderStateReason {
	NO_BUILDING_SELECTED,
	NO_TILE_DATA,
	REPLACEABLE,
	SITE_OCCUPIED,
	B<PERSON><PERSON>ABLE,
}

const TRANSPARENT_RED: Color = Color(Color.RED, 0.5)
const TRANSPARENT_GREEN: Color = Color(Color.GREEN, 0.5)
const TRANSPARENT_YELLOW: Color = Color(Color.YELLOW, 0.5)
const MAX_DEGREES: int = 360

var selected_building_stats: BuildingStats = null
var occupied_tiles: Dictionary[Vector2i, Building]
var sync_nodes: Dictionary[BuildingType.Enum, AnimatedSprite2D] = {}

var building_rotation: int = 0:
	set(value):
		building_rotation = value
		if building_rotation < 0:
			building_rotation += MAX_DEGREES
		building_rotation %= MAX_DEGREES
	get:
		return building_rotation


## Creates the builder state dictionary with standardized names of entries.
func _create_builder_state(can_build: bool, reason: BuilderStateReason, color: Color) -> Dictionary:
	return {
		"can_build": can_build,
		"reason": reason,
		"color": color,
	}


func _ready() -> void:
	SaveManager.planet_data_changed.connect(func(_planet: PlanetData) -> void: reset())

#TODO WFT if this function name??
## Centralized buildability check
# Returns a dictionary with keys: can_build (bool), reason (String), color (Color)
func can_build_at_position(
	tilemap: TileMapLayer, tilemap_position: Vector2i, building_stats: BuildingStats = null
) -> Dictionary:
	if building_stats == null:
		building_stats = selected_building_stats

	if not building_stats:
		return _create_builder_state(false, BuilderStateReason.NO_BUILDING_SELECTED, TRANSPARENT_RED)

	if not is_building_site_buildable(tilemap, tilemap_position, selected_building_stats.dimensions):
		return _create_builder_state(false, BuilderStateReason.NO_TILE_DATA, TRANSPARENT_RED)

	if is_building_replaceable_at_position(tilemap_position):
		return _create_builder_state(true, BuilderStateReason.REPLACEABLE, TRANSPARENT_YELLOW)

	if is_building_site_occupied(tilemap_position, selected_building_stats.dimensions):
		return _create_builder_state(false, BuilderStateReason.SITE_OCCUPIED, TRANSPARENT_RED)

	return _create_builder_state(true, BuilderStateReason.BUILDABLE, TRANSPARENT_GREEN)


## Sets tiles occupied by a building as free -- deletes them from the occupied tiles.
func set_building_sites_as_free(building: Building) -> void:
	for tile in building.tiles_occupied_by_building:
		occupied_tiles.erase(tile)


## Gets building at specified position in buildings tilemap. Might return null if there
## is no building at said position.
func get_building_at_position(tilemap_position: Vector2i) -> Building:
	var building: Node2D = occupied_tiles.get(tilemap_position)
	return building


## Checks whether it is possible to replace building at given position.
func is_building_replaceable_at_position(tilemap_position: Vector2i) -> bool:
	var replaceable_building: Building = get_building_at_position(tilemap_position)
	if not replaceable_building:
		return false
	if not replaceable_building.stats.is_replaceable:
		return false
	if replaceable_building.stats.building_type != selected_building_stats.building_type:
		return false
	return true


## Sets tiles occupied by given building as occupied.[br]
## It does not do any checks and simply rewrites any data that might be saved in occupied
## tiles beforehand.
func set_tiles_as_occupied(building: Building) -> void:
	for tile_coordinate in building.tiles_occupied_by_building:
		BuildingModeManager.occupied_tiles[tile_coordinate] = building


## Checks whether it is possible to build a building at required tile positions.
func is_building_site_occupied(tilemap_position: Vector2i, dimensions: Vector2i) -> bool:
	for x_offset in range(dimensions.x):
		for y_offset in range(dimensions.y):
			var subposition := tilemap_position + Vector2i(x_offset, y_offset)
			if subposition in occupied_tiles:
				return true
	return false


## Checks whether it is possible to build a building at required tile positions.
func is_building_site_buildable(tilemap: TileMapLayer, tilemap_position: Vector2i, dimensions: Vector2i) -> bool:
	for x_offset in range(dimensions.x):
		for y_offset in range(dimensions.y):
			var subposition := tilemap_position + Vector2i(x_offset, y_offset)
			if not tilemap.get_cell_tile_data(subposition):
				return false
	return true


## Clears the state of building manager.
func reset() -> void:
	LogManager.debug("Resetting building mode manager", self)
	selected_building_stats = null
	building_rotation = 0
	occupied_tiles.clear()


func _sync_animation(instance: Building) -> void:
	if not instance.stats.sync_animation:
		return

	var animations: AnimatedSprite2D = instance.animation

	if not animations.sprite_frames:
		return

	animations.animation = "East" if instance.stats.is_rotatable else "North"

	var sync_node: AnimatedSprite2D = sync_nodes.get(instance.stats.building_type)

	if not sync_node:
		var new_sync_node: AnimatedSprite2D = AnimatedSprite2D.new()
		new_sync_node.sprite_frames = animations.sprite_frames
		new_sync_node.animation = animations.animation
		new_sync_node.play()
		new_sync_node.visible = false
		new_sync_node.name = str(instance.stats.building_type)
		if is_inside_tree() == true:
			add_child(new_sync_node)

		sync_nodes[instance.stats.building_type] = new_sync_node
		sync_node = new_sync_node

	if sync_node:
		animations.set_frame_and_progress(sync_node.frame, sync_node.frame_progress)
		animations.play()

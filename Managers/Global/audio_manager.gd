class_name AudioManagerScript
extends Node2D

## Stores all possible SoundEffects that can be played.
@export var sound_effects: Array[SoundEffect]
var active_sound_effects: Dictionary[SoundEffect,int]

## Loads all registered SoundEffects on ready as a reference.
var sound_effect_dict: Dictionary = {}


func _ready() -> void:
	for sound_effect: SoundEffect in sound_effects:
		sound_effect_dict[sound_effect.type] = sound_effect


func create_audio(type: SoundEffect.SOUND_EFFECT_TYPE) -> void:
	if sound_effect_dict.has(type):
		var sound_effect: SoundEffect = sound_effect_dict[type]
		
		var count: int = active_sound_effects.get(sound_effect, 0)
		if count >= sound_effect.limit:
			return  # Prevent playing more than allowed
		
		active_sound_effects[sound_effect] = count + 1

		var sound: AudioStreamPlayer = AudioStreamPlayer.new()
		add_child(sound)
		sound.stream = sound_effect.sound_effect
		sound.volume_db = sound_effect.volume
		sound.pitch_scale = sound_effect.pitch_scale + randf_range(-sound_effect.pitch_randomness, sound_effect.pitch_randomness)
		
		# Define a cleanup function
		var _on_audio_finished: Callable = func() -> void:
			if sound_effect in active_sound_effects:
				active_sound_effects[sound_effect] = max(0, active_sound_effects[sound_effect] - 1)
				if active_sound_effects[sound_effect] == 0:
					active_sound_effects.erase(sound_effect)
		
		# Connect both finished and tree_exited to cleanup
		sound.finished.connect(_on_audio_finished)
		sound.tree_exited.connect(_on_audio_finished)
		sound.play()

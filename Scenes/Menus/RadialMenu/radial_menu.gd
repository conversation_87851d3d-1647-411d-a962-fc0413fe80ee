# MUST BE TOOL TO UPDATE UI IN EDITOR
@tool
extends Control

# Mostly donated from https://www.youtube.com/watch?v=TtziEJZtWXc&ab_channel=SquadronStudio

@export var background_color: Color = Color(0.2, 0.2, 0.2)
@export var line_color: Color = Color.GRAY
@export var highlight_color: Color = Color.WEB_GRAY

@export var outer_radius: int = 256

@export_range(50.0, 300.0, 1.0) var inner_radius: float = 96.0
@export_range(1.0, 15.0, 1.0) var line_width: float = 5.0
@export_range(2, 10, 1) var circle_resolution: int = 2

@export var options: Array[RadialMenuOption] = []

var _select: int = 0
var _origin: Vector2 = Vector2.ONE
var _toggled: bool = false


func _ready() -> void:
	hide()

	if Engine.is_editor_hint():
		return

	_hide_menu()


func _draw() -> void:
	var sprite_size: Vector2 = Vector2(inner_radius, inner_radius)

	# for centering images
	var offset: Vector2 = sprite_size / -2

	var point_array: PackedVector2Array = []
	for i in range((len(options) - 1) * circle_resolution):
		var direction_vector: Vector2 = Vector2.from_angle(TAU * i / ((len(options) - 1) * circle_resolution))
		point_array.append(direction_vector * outer_radius)

	if len(options) <= 3:
		draw_circle(Vector2.ZERO, outer_radius, background_color)
		draw_arc(Vector2.ZERO, inner_radius, 0, TAU, 32, line_color, line_width)
	else:
		draw_polygon(point_array, [background_color])
		draw_arc(Vector2.ZERO, inner_radius, 0, TAU, (len(options) - 1) * circle_resolution + 1, line_color, line_width)

	if len(options) >= 3:
		for i in range(len(options) - 1):
			var rads: float = TAU * i / (len(options) - 1)
			var point: Vector2 = Vector2.from_angle(rads)
			draw_line(point * inner_radius, point * outer_radius, line_color, line_width)

		if _select == 0:
			point_array.clear()
			for i in range((len(options) - 1) * circle_resolution):
				var direction_vector: Vector2 = Vector2.from_angle(TAU * i / ((len(options) - 1) * circle_resolution))
				point_array.append(direction_vector * (inner_radius - line_width / 2))
			draw_polygon(point_array, [highlight_color])

		draw_texture_rect_region(options[0].atlas, Rect2(offset, sprite_size), options[0].region)

		for i in range(1, len(options)):
			if not options[i]:
				continue
			var start_rads: float = (TAU * (i - 1)) / (len(options) - 1)
			var end_rads: float = (TAU * (i)) / (len(options) - 1)
			# -1 because godot has y in negative direction compared to math
			var mid_point: float = (start_rads + end_rads) / 2.0 * -1.0
			var mid_radius: float = (inner_radius + outer_radius) / 2

			if _select == i:
				var points_per_arc: int = circle_resolution
				var points_inner: PackedVector2Array = PackedVector2Array()
				var points_outer: PackedVector2Array = PackedVector2Array()

				# Pushes the endges slightly in to accomodate for line width
				start_rads += line_width * 0.002
				end_rads -= line_width * 0.002

				for j in range(points_per_arc + 1):
					var angle: float = start_rads + j * (end_rads - start_rads) / points_per_arc
					points_inner.append((inner_radius + line_width / 2) * Vector2.from_angle(TAU - angle))
					points_outer.append(outer_radius * Vector2.from_angle(TAU - angle))

				points_outer.reverse()

				draw_polygon(points_inner + points_outer, [highlight_color])

			var draw_pos: Vector2 = mid_radius * Vector2.from_angle(mid_point) + offset
			draw_texture_rect_region(options[i].atlas, Rect2(draw_pos, sprite_size), options[i].region)


func _process(_delta: float) -> void:
	if not Engine.is_editor_hint() and not _toggled:
		return

	var mouse_pos: Vector2 = get_global_mouse_position()
	var mouse_radius: float

	# Editor cant set _origin
	if Engine.is_editor_hint():
		mouse_radius = (mouse_pos).length()
	else:
		mouse_radius = (mouse_pos - _origin).length()
		mouse_pos = (mouse_pos - _origin)

	if mouse_radius < inner_radius:
		_select = 0
	else:
		var mouse_rads: float = fposmod(mouse_pos.angle() * -1, TAU)
		_select = ceil((mouse_rads / TAU) * (len(options) - 1))

	queue_redraw()


func _unhandled_key_input(event: InputEvent) -> void:
	if event.is_action_pressed("OpenRadialMenu"):
		_show_menu()
		_origin = get_global_mouse_position()
		global_position = _origin
	elif event.is_action_released("OpenRadialMenu"):
		_hide_menu()


func _show_menu() -> void:
	show()
	_toggled = true
	var tween: Tween = create_tween()
	tween.tween_property(self, "scale", Vector2.ONE, 0.05)

	# Important to prevent spamming
	tween.finished.connect(show)


func _hide_menu() -> void:
	_toggled = false
	LogManager.debug("Activating " + options[_select].name, self)
	options[_select].activate()
	var tween: Tween = create_tween()
	tween.tween_property(self, "scale", Vector2(0.1, 0.1), 0.05)
	tween.finished.connect(hide)

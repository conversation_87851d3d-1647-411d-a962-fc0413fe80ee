[gd_scene load_steps=27 format=3 uid="uid://ck5jwl6m0hosg"]

[ext_resource type="Script" uid="uid://btc6te8igj7f8" path="res://Scenes/Menus/space_map_menu.gd" id="1_d61je"]
[ext_resource type="PackedScene" uid="uid://bm2niqbcccmqu" path="res://Entities/NodeMap/SpaceMap/planet_node.tscn" id="2_8u05b"]
[ext_resource type="Texture2D" uid="uid://iqucn2d118d3" path="res://assets/Sprites/32x32/HomeIcon.png" id="3_vxj4d"]
[ext_resource type="PackedScene" uid="uid://0ihbkwcfs6yp" path="res://Scenes/Playable/Start planet/PlanetSurface.tscn" id="3_xws8f"]
[ext_resource type="LabelSettings" uid="uid://dybh11bdm6mcy" path="res://assets/Text/Header.tres" id="6_a6bim"]
[ext_resource type="PackedScene" uid="uid://ba2ok888yp5v4" path="res://Scripts/camera.tscn" id="6_mkcxx"]
[ext_resource type="Texture2D" uid="uid://imyxnfr07jri" path="res://assets/Sprites/32x32/ResearchIcon.png" id="7_vxj4d"]
[ext_resource type="PackedScene" uid="uid://bo5oaq1ehu5wb" path="res://Entities/NodeMap/camera_limiter.tscn" id="9_yq2x2"]
[ext_resource type="SpriteFrames" uid="uid://ijwcel57h7yx" path="res://assets/Sprites/Animations/NoAtmoPlanetLowResAnimation.tres" id="11_lb1af"]
[ext_resource type="SpriteFrames" uid="uid://t6d5mdgfkqt1" path="res://assets/Sprites/Animations/NoAtmoPlanetHighResAnimation.tres" id="12_fxcwo"]
[ext_resource type="Script" uid="uid://dj4gow3614evw" path="res://Managers/Local/WinConditions/win_condition_data.gd" id="12_mkcxx"]
[ext_resource type="PackedScene" uid="uid://jpvn8pcdkofx" path="res://Scenes/Playable/Tutorial planet/TutorialPlanetSurface.tscn" id="13_x1enc"]
[ext_resource type="SpriteFrames" uid="uid://c436giv0qrhya" path="res://assets/Sprites/Animations/DryPlanetLowResAnimation.tres" id="14_fqpah"]
[ext_resource type="SpriteFrames" uid="uid://b7hsxxp5xhgs0" path="res://assets/Sprites/Animations/DryPlanetHighResAnimation.tres" id="15_21dg0"]
[ext_resource type="SpriteFrames" uid="uid://bmyryk3i5m2rd" path="res://assets/Sprites/Animations/IceGiantLowResAnimation.tres" id="16_mkcxx"]
[ext_resource type="SpriteFrames" uid="uid://b6fvf7w4bgrne" path="res://assets/Sprites/Animations/IceGiantHighResAnimation.tres" id="17_nikd1"]
[ext_resource type="SpriteFrames" uid="uid://noyhsqcsiit0" path="res://assets/Sprites/Animations/LavaWorldLowResAnimation.tres" id="18_yq2x2"]
[ext_resource type="SpriteFrames" uid="uid://bxhnycsl351ok" path="res://assets/Sprites/Animations/LavaWorldHighResAnimation.tres" id="19_m53l4"]
[ext_resource type="SpriteFrames" uid="uid://dyva0l58yvrur" path="res://assets/Sprites/Animations/TerranPlanetLowResAnimation.tres" id="20_vxj4d"]
[ext_resource type="SpriteFrames" uid="uid://xd88qf7pa3o7" path="res://assets/Sprites/Animations/TerranPlanetHighResAnimation.tres" id="21_gudg2"]

[sub_resource type="Resource" id="Resource_helbh"]
script = ExtResource("12_mkcxx")
target_item = 7
target_building = 0
target_amount = 10
win_condition_type = 0
status = 0
metadata/_custom_type_script = "uid://dj4gow3614evw"

[sub_resource type="Resource" id="Resource_t0jle"]
script = ExtResource("12_mkcxx")
target_item = 18
target_building = 0
target_amount = 15
win_condition_type = 2
status = 0
metadata/_custom_type_script = "uid://dj4gow3614evw"

[sub_resource type="Resource" id="Resource_ltxql"]
script = ExtResource("12_mkcxx")
target_item = 0
target_building = 1
target_amount = 25
win_condition_type = 1
status = 0
metadata/_custom_type_script = "uid://dj4gow3614evw"

[sub_resource type="Resource" id="Resource_f6vgh"]
script = ExtResource("12_mkcxx")
target_item = 16
target_building = 0
target_amount = 10
win_condition_type = 0
status = 0
metadata/_custom_type_script = "uid://dj4gow3614evw"

[sub_resource type="Resource" id="Resource_eppmy"]
script = ExtResource("12_mkcxx")
target_item = 4
target_building = 0
target_amount = 10
win_condition_type = 0
status = 0
metadata/_custom_type_script = "uid://dj4gow3614evw"

[sub_resource type="Resource" id="Resource_sglyb"]
script = ExtResource("12_mkcxx")
target_item = 0
target_building = 0
target_amount = 10
win_condition_type = 0
status = 0
metadata/_custom_type_script = "uid://dj4gow3614evw"

[node name="SpaceMapMenu" type="Node2D"]
script = ExtResource("1_d61je")

[node name="SpaceMapUI" type="CanvasLayer" parent="."]

[node name="MarginContainer" type="MarginContainer" parent="SpaceMapUI"]
anchors_preset = 10
anchor_right = 1.0
offset_bottom = 70.0
grow_horizontal = 2
size_flags_horizontal = 3

[node name="Label" type="Label" parent="SpaceMapUI/MarginContainer"]
layout_mode = 2
size_flags_horizontal = 6
text = "PLANET SELECTION"
label_settings = ExtResource("6_a6bim")

[node name="HBoxContainer" type="HBoxContainer" parent="SpaceMapUI/MarginContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="LeftMenu" type="HBoxContainer" parent="SpaceMapUI/MarginContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 2
theme_override_constants/separation = 10

[node name="BackToMainmenuButton" type="Button" parent="SpaceMapUI/MarginContainer/HBoxContainer/LeftMenu"]
custom_minimum_size = Vector2(50, 50)
layout_mode = 2
theme_type_variation = &"FilledButton"
text = " Back To Menu"
icon = ExtResource("3_vxj4d")

[node name="RightMenu" type="HBoxContainer" parent="SpaceMapUI/MarginContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 10
theme_override_constants/separation = 10

[node name="ResearchMenuButton" type="Button" parent="SpaceMapUI/MarginContainer/HBoxContainer/RightMenu"]
custom_minimum_size = Vector2(50, 50)
layout_mode = 2
theme_type_variation = &"FilledButton"
text = " Research"
icon = ExtResource("7_vxj4d")

[node name="Camera2D" parent="." node_paths=PackedStringArray("border") instance=ExtResource("6_mkcxx")]
border = NodePath("../CameraLimiter")

[node name="CameraLimiter" parent="." instance=ExtResource("9_yq2x2")]
limiter_multiplier = 3.0

[node name="Planets" type="Node2D" parent="."]

[node name="Planet1" parent="Planets" node_paths=PackedStringArray("linked_nodes") instance=ExtResource("2_8u05b")]
position = Vector2(100, 85)
planet_scene = ExtResource("3_xws8f")
high_res_animation = ExtResource("12_fxcwo")
low_res_animation = ExtResource("11_lb1af")
win_conditions = Array[ExtResource("12_mkcxx")]([SubResource("Resource_helbh"), SubResource("Resource_t0jle"), SubResource("Resource_ltxql")])
linked_nodes = [NodePath("../Planet2"), NodePath("../Planet3")]
node_state = 2

[node name="Planet2" parent="Planets" instance=ExtResource("2_8u05b")]
position = Vector2(376, 555)
planet_scene = ExtResource("13_x1enc")
high_res_animation = ExtResource("15_21dg0")
low_res_animation = ExtResource("14_fqpah")
win_conditions = Array[ExtResource("12_mkcxx")]([SubResource("Resource_f6vgh"), SubResource("Resource_eppmy"), SubResource("Resource_sglyb")])

[node name="Planet3" parent="Planets" node_paths=PackedStringArray("linked_nodes") instance=ExtResource("2_8u05b")]
position = Vector2(780, 222)
planet_scene = ExtResource("3_xws8f")
high_res_animation = ExtResource("17_nikd1")
low_res_animation = ExtResource("16_mkcxx")
linked_nodes = [NodePath("../Planet4")]

[node name="Planet4" parent="Planets" node_paths=PackedStringArray("linked_nodes") instance=ExtResource("2_8u05b")]
position = Vector2(1244, 287)
planet_scene = ExtResource("3_xws8f")
high_res_animation = ExtResource("19_m53l4")
low_res_animation = ExtResource("18_yq2x2")
linked_nodes = [NodePath("../Planet5")]

[node name="Planet5" parent="Planets" instance=ExtResource("2_8u05b")]
position = Vector2(1539, 774)
planet_scene = ExtResource("3_xws8f")
high_res_animation = ExtResource("21_gudg2")
low_res_animation = ExtResource("20_vxj4d")

[connection signal="pressed" from="SpaceMapUI/MarginContainer/HBoxContainer/LeftMenu/BackToMainmenuButton" to="." method="_on_back_to_main_menu_button_pressed"]
[connection signal="pressed" from="SpaceMapUI/MarginContainer/HBoxContainer/RightMenu/ResearchMenuButton" to="." method="_on_research_button_pressed"]
[connection signal="resized" from="CameraLimiter" to="Camera2D" method="_on_border_resized"]

[gd_resource type="Resource" script_class="PlanetData" load_steps=141 format=3 uid="uid://cnq281ga47738"]

[ext_resource type="Script" uid="uid://buyi0km53ceyq" path="res://Scripts/building_data.gd" id="1_tudf1"]
[ext_resource type="Resource" uid="uid://b41uoarpo0cmu" path="res://Entities/Buildings/mine/mine.tres" id="2_nbv8r"]
[ext_resource type="Script" uid="uid://h5swltwpuw8s" path="res://Entities/Components/Power/power_buffer_component.gd" id="3_8bejo"]
[ext_resource type="Resource" uid="uid://dt84gufsp5ag6" path="res://Entities/Buildings/conveyor belt/conveyor_belt.tres" id="4_ytttp"]
[ext_resource type="Resource" uid="uid://x0fd41s4t43f" path="res://Entities/Buildings/smeltery/smeltery.tres" id="5_mhld2"]
[ext_resource type="Resource" uid="uid://dg43rxhtrt7vk" path="res://Entities/Buildings/Inserter/inserter_long.tres" id="6_6rvuy"]
[ext_resource type="Resource" uid="uid://4n3lekspwh6s" path="res://Entities/Buildings/base/base.tres" id="7_vl4f6"]
[ext_resource type="Resource" uid="uid://cg0uhaqc3hn0r" path="res://Entities/Buildings/power_plant.tres" id="8_i5kel"]
[ext_resource type="Resource" uid="uid://djfskeupp2xap" path="res://Entities/Buildings/Inserter/inserter.tres" id="9_tn5ax"]
[ext_resource type="Resource" uid="uid://blcu6tjgdxvah" path="res://Entities/Buildings/simple processing/saw.tres" id="10_o7beu"]
[ext_resource type="Resource" uid="uid://cfyixy0lwgqpy" path="res://Entities/Buildings/assembler/assembler.tres" id="11_pjky8"]
[ext_resource type="Script" uid="uid://dq2b03205hb1p" path="res://Scripts/level_inventory.gd" id="12_r0up7"]
[ext_resource type="Script" uid="uid://cucag3ihib10a" path="res://Managers/Global/save_manager/planet_data.gd" id="13_l7iy4"]
[ext_resource type="Script" uid="uid://c7s4elarc1emd" path="res://Entities/Buildings/building base/building_stats.gd" id="14_qqa7k"]
[ext_resource type="Resource" uid="uid://c5pwi56ffyvyw" path="res://Entities/Buildings/simple processing/press.tres" id="15_ulv85"]
[ext_resource type="Resource" uid="uid://e56ikko2lqo5" path="res://Entities/Buildings/Inserter/inserter_fast.tres" id="16_ylmbn"]
[ext_resource type="Script" uid="uid://dj4gow3614evw" path="res://Managers/Local/WinConditions/win_condition_data.gd" id="17_f05h1"]

[sub_resource type="Resource" id="Resource_uu54a"]
script = ExtResource("1_tudf1")
position = Vector2i(-784, 80)
rotation_degrees = 0
building_stats = ExtResource("2_nbv8r")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 50.0,
"stored_power": 41.6523
}
})

[sub_resource type="Resource" id="Resource_fklql"]
script = ExtResource("1_tudf1")
position = Vector2i(-784, 112)
rotation_degrees = 0
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_qaksm"]
script = ExtResource("1_tudf1")
position = Vector2i(-752, 80)
rotation_degrees = 0
building_stats = ExtResource("2_nbv8r")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 50.0,
"stored_power": 41.6523
}
})

[sub_resource type="Resource" id="Resource_no4bc"]
script = ExtResource("1_tudf1")
position = Vector2i(-752, 112)
rotation_degrees = 90
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_xbb8n"]
script = ExtResource("1_tudf1")
position = Vector2i(-752, 144)
rotation_degrees = 90
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_7c357"]
script = ExtResource("1_tudf1")
position = Vector2i(-752, 176)
rotation_degrees = 90
building_stats = ExtResource("5_mhld2")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 50.0,
"stored_power": 50.0
}
})

[sub_resource type="Resource" id="Resource_ot7kx"]
script = ExtResource("1_tudf1")
position = Vector2i(-752, 208)
rotation_degrees = 0
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_7i310"]
script = ExtResource("1_tudf1")
position = Vector2i(-720, 80)
rotation_degrees = 0
building_stats = ExtResource("2_nbv8r")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 50.0,
"stored_power": 41.6523
}
})

[sub_resource type="Resource" id="Resource_c8rgq"]
script = ExtResource("1_tudf1")
position = Vector2i(-720, 112)
rotation_degrees = 90
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_rwaoi"]
script = ExtResource("1_tudf1")
position = Vector2i(-720, 144)
rotation_degrees = 90
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_ffqhp"]
script = ExtResource("1_tudf1")
position = Vector2i(-720, 176)
rotation_degrees = 90
building_stats = ExtResource("5_mhld2")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 50.0,
"stored_power": 50.0
}
})

[sub_resource type="Resource" id="Resource_do47a"]
script = ExtResource("1_tudf1")
position = Vector2i(-720, 208)
rotation_degrees = 0
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_xri4q"]
script = ExtResource("1_tudf1")
position = Vector2i(-688, 80)
rotation_degrees = 0
building_stats = ExtResource("2_nbv8r")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 50.0,
"stored_power": 41.6523
}
})

[sub_resource type="Resource" id="Resource_08dp8"]
script = ExtResource("1_tudf1")
position = Vector2i(-688, 112)
rotation_degrees = 180
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_jw53m"]
script = ExtResource("1_tudf1")
position = Vector2i(-688, 208)
rotation_degrees = 0
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_18o26"]
script = ExtResource("1_tudf1")
position = Vector2i(-656, 208)
rotation_degrees = 0
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_j06nk"]
script = ExtResource("1_tudf1")
position = Vector2i(-624, 80)
rotation_degrees = 0
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_g6whg"]
script = ExtResource("1_tudf1")
position = Vector2i(-624, 112)
rotation_degrees = 270
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_ucc0a"]
script = ExtResource("1_tudf1")
position = Vector2i(-624, 176)
rotation_degrees = 90
building_stats = ExtResource("6_6rvuy")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 50.0,
"stored_power": 50.0
}
})

[sub_resource type="Resource" id="Resource_5b0q6"]
script = ExtResource("1_tudf1")
position = Vector2i(-624, 208)
rotation_degrees = 0
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_7gv38"]
script = ExtResource("1_tudf1")
position = Vector2i(-624, 240)
rotation_degrees = 0
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_j444j"]
script = ExtResource("1_tudf1")
position = Vector2i(-624, 272)
rotation_degrees = 270
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_va50t"]
script = ExtResource("1_tudf1")
position = Vector2i(-624, 304)
rotation_degrees = 270
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_503xr"]
script = ExtResource("1_tudf1")
position = Vector2i(-624, 336)
rotation_degrees = 270
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_3p6hy"]
script = ExtResource("1_tudf1")
position = Vector2i(-624, 368)
rotation_degrees = 270
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_kmbwl"]
script = ExtResource("1_tudf1")
position = Vector2i(-624, 400)
rotation_degrees = 270
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_7pptx"]
script = ExtResource("1_tudf1")
position = Vector2i(-624, 432)
rotation_degrees = 270
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_xtidg"]
script = ExtResource("1_tudf1")
position = Vector2i(-624, 464)
rotation_degrees = 270
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_mt3ri"]
script = ExtResource("1_tudf1")
position = Vector2i(-592, 80)
rotation_degrees = 0
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_5f03g"]
script = ExtResource("1_tudf1")
position = Vector2i(-592, 208)
rotation_degrees = 0
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_li6nb"]
script = ExtResource("1_tudf1")
position = Vector2i(-592, 464)
rotation_degrees = 180
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_cbtk1"]
script = ExtResource("1_tudf1")
position = Vector2i(-560, 208)
rotation_degrees = 0
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_st3vt"]
script = ExtResource("1_tudf1")
position = Vector2i(-560, 464)
rotation_degrees = 180
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_ffc6d"]
script = ExtResource("1_tudf1")
position = Vector2i(-528, 16)
rotation_degrees = 0
building_stats = ExtResource("7_vl4f6")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_1qjve"]
script = ExtResource("1_tudf1")
position = Vector2i(-528, 208)
rotation_degrees = 0
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_eqie4"]
script = ExtResource("1_tudf1")
position = Vector2i(-528, 464)
rotation_degrees = 180
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_1g3ya"]
script = ExtResource("1_tudf1")
position = Vector2i(-496, 464)
rotation_degrees = 180
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_6qcyr"]
script = ExtResource("1_tudf1")
position = Vector2i(-464, 464)
rotation_degrees = 180
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_bg7vr"]
script = ExtResource("1_tudf1")
position = Vector2i(-432, 464)
rotation_degrees = 180
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_hfqbf"]
script = ExtResource("1_tudf1")
position = Vector2i(-400, 464)
rotation_degrees = 180
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_un7r1"]
script = ExtResource("1_tudf1")
position = Vector2i(-368, -304)
rotation_degrees = 0
building_stats = ExtResource("8_i5kel")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 2000.0,
"stored_power": 1622.37
}
})

[sub_resource type="Resource" id="Resource_2chwe"]
script = ExtResource("1_tudf1")
position = Vector2i(-368, -240)
rotation_degrees = 0
building_stats = ExtResource("8_i5kel")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 2000.0,
"stored_power": 1622.37
}
})

[sub_resource type="Resource" id="Resource_x8qhh"]
script = ExtResource("1_tudf1")
position = Vector2i(-368, 464)
rotation_degrees = 180
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_wxipc"]
script = ExtResource("1_tudf1")
position = Vector2i(-336, 16)
rotation_degrees = 90
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_1ryqs"]
script = ExtResource("1_tudf1")
position = Vector2i(-336, 400)
rotation_degrees = 90
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_dka8w"]
script = ExtResource("1_tudf1")
position = Vector2i(-336, 432)
rotation_degrees = 90
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_14vk3"]
script = ExtResource("1_tudf1")
position = Vector2i(-336, 464)
rotation_degrees = 180
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_eswxr"]
script = ExtResource("1_tudf1")
position = Vector2i(-304, -304)
rotation_degrees = 0
building_stats = ExtResource("9_tn5ax")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 25.0,
"stored_power": 25.0
}
})

[sub_resource type="Resource" id="Resource_t58x5"]
script = ExtResource("1_tudf1")
position = Vector2i(-304, -272)
rotation_degrees = 0
building_stats = ExtResource("9_tn5ax")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 25.0,
"stored_power": 25.0
}
})

[sub_resource type="Resource" id="Resource_pwcnk"]
script = ExtResource("1_tudf1")
position = Vector2i(-304, -240)
rotation_degrees = 0
building_stats = ExtResource("9_tn5ax")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 25.0,
"stored_power": 25.0
}
})

[sub_resource type="Resource" id="Resource_xs7mu"]
script = ExtResource("1_tudf1")
position = Vector2i(-304, -208)
rotation_degrees = 0
building_stats = ExtResource("9_tn5ax")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 25.0,
"stored_power": 25.0
}
})

[sub_resource type="Resource" id="Resource_4nmii"]
script = ExtResource("1_tudf1")
position = Vector2i(-304, 16)
rotation_degrees = 0
building_stats = ExtResource("9_tn5ax")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 25.0,
"stored_power": 25.0
}
})

[sub_resource type="Resource" id="Resource_86oao"]
script = ExtResource("1_tudf1")
position = Vector2i(-304, 400)
rotation_degrees = 180
building_stats = ExtResource("10_o7beu")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 50.0,
"stored_power": 50.0
}
})

[sub_resource type="Resource" id="Resource_2n7j5"]
script = ExtResource("1_tudf1")
position = Vector2i(-304, 464)
rotation_degrees = 180
building_stats = ExtResource("10_o7beu")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 50.0,
"stored_power": 50.0
}
})

[sub_resource type="Resource" id="Resource_3xf7j"]
script = ExtResource("1_tudf1")
position = Vector2i(-272, -304)
rotation_degrees = 180
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_cdwfe"]
script = ExtResource("1_tudf1")
position = Vector2i(-272, -272)
rotation_degrees = 180
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_mvgjq"]
script = ExtResource("1_tudf1")
position = Vector2i(-272, -240)
rotation_degrees = 180
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_sig4l"]
script = ExtResource("1_tudf1")
position = Vector2i(-272, -208)
rotation_degrees = 180
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_abo7a"]
script = ExtResource("1_tudf1")
position = Vector2i(-272, -16)
rotation_degrees = 0
building_stats = ExtResource("11_pjky8")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 50.0,
"stored_power": 50.0
}
})

[sub_resource type="Resource" id="Resource_6xd84"]
script = ExtResource("1_tudf1")
position = Vector2i(-272, 400)
rotation_degrees = 180
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_i7h8q"]
script = ExtResource("1_tudf1")
position = Vector2i(-272, 464)
rotation_degrees = 180
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_n4ijd"]
script = ExtResource("1_tudf1")
position = Vector2i(-240, -304)
rotation_degrees = 0
building_stats = ExtResource("2_nbv8r")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 50.0,
"stored_power": 41.6523
}
})

[sub_resource type="Resource" id="Resource_ug2rn"]
script = ExtResource("1_tudf1")
position = Vector2i(-240, -272)
rotation_degrees = 0
building_stats = ExtResource("2_nbv8r")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 50.0,
"stored_power": 41.6523
}
})

[sub_resource type="Resource" id="Resource_r2pqv"]
script = ExtResource("1_tudf1")
position = Vector2i(-240, -240)
rotation_degrees = 0
building_stats = ExtResource("2_nbv8r")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 50.0,
"stored_power": 41.6523
}
})

[sub_resource type="Resource" id="Resource_2v30n"]
script = ExtResource("1_tudf1")
position = Vector2i(-240, -208)
rotation_degrees = 0
building_stats = ExtResource("2_nbv8r")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 50.0,
"stored_power": 41.6523
}
})

[sub_resource type="Resource" id="Resource_c75wk"]
script = ExtResource("1_tudf1")
position = Vector2i(-240, 400)
rotation_degrees = 180
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_lc2dk"]
script = ExtResource("1_tudf1")
position = Vector2i(-240, 432)
rotation_degrees = 90
building_stats = ExtResource("9_tn5ax")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 25.0,
"stored_power": 25.0
}
})

[sub_resource type="Resource" id="Resource_p8a22"]
script = ExtResource("1_tudf1")
position = Vector2i(-240, 464)
rotation_degrees = 180
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_feq5p"]
script = ExtResource("1_tudf1")
position = Vector2i(-208, -16)
rotation_degrees = 0
building_stats = ExtResource("6_6rvuy")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 50.0,
"stored_power": 50.0
}
})

[sub_resource type="Resource" id="Resource_7l1x2"]
script = ExtResource("1_tudf1")
position = Vector2i(-208, 48)
rotation_degrees = 0
building_stats = ExtResource("6_6rvuy")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 50.0,
"stored_power": 50.0
}
})

[sub_resource type="Resource" id="Resource_wqjcu"]
script = ExtResource("1_tudf1")
position = Vector2i(-208, 464)
rotation_degrees = 180
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_oxetg"]
script = ExtResource("1_tudf1")
position = Vector2i(-176, 16)
rotation_degrees = 0
building_stats = ExtResource("6_6rvuy")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 50.0,
"stored_power": 50.0
}
})

[sub_resource type="Resource" id="Resource_rggi8"]
script = ExtResource("1_tudf1")
position = Vector2i(-176, 80)
rotation_degrees = 0
building_stats = ExtResource("6_6rvuy")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 50.0,
"stored_power": 50.0
}
})

[sub_resource type="Resource" id="Resource_rsfxe"]
script = ExtResource("1_tudf1")
position = Vector2i(-176, 464)
rotation_degrees = 180
building_stats = ExtResource("5_mhld2")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 50.0,
"stored_power": 50.0
}
})

[sub_resource type="Resource" id="Resource_7uviv"]
script = ExtResource("1_tudf1")
position = Vector2i(-144, -304)
rotation_degrees = 90
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_w1xrs"]
script = ExtResource("1_tudf1")
position = Vector2i(-144, -272)
rotation_degrees = 90
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_hc3gy"]
script = ExtResource("1_tudf1")
position = Vector2i(-144, -240)
rotation_degrees = 90
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_2pond"]
script = ExtResource("1_tudf1")
position = Vector2i(-144, -208)
rotation_degrees = 90
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_7dpd5"]
script = ExtResource("1_tudf1")
position = Vector2i(-144, -176)
rotation_degrees = 90
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_3o2nc"]
script = ExtResource("1_tudf1")
position = Vector2i(-144, -144)
rotation_degrees = 90
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_soiku"]
script = ExtResource("1_tudf1")
position = Vector2i(-144, -112)
rotation_degrees = 90
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_48j0d"]
script = ExtResource("1_tudf1")
position = Vector2i(-144, -80)
rotation_degrees = 90
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_qgy4y"]
script = ExtResource("1_tudf1")
position = Vector2i(-144, -48)
rotation_degrees = 90
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_h3t0j"]
script = ExtResource("1_tudf1")
position = Vector2i(-144, -16)
rotation_degrees = 90
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_ebp3b"]
script = ExtResource("1_tudf1")
position = Vector2i(-144, 16)
rotation_degrees = 90
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_1omrx"]
script = ExtResource("1_tudf1")
position = Vector2i(-144, 48)
rotation_degrees = 90
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_rb680"]
script = ExtResource("1_tudf1")
position = Vector2i(-144, 80)
rotation_degrees = 90
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_tubii"]
script = ExtResource("1_tudf1")
position = Vector2i(-144, 464)
rotation_degrees = 180
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_wgfge"]
script = ExtResource("1_tudf1")
position = Vector2i(-112, -304)
rotation_degrees = 0
building_stats = ExtResource("2_nbv8r")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 50.0,
"stored_power": 41.6523
}
})

[sub_resource type="Resource" id="Resource_bbefk"]
script = ExtResource("1_tudf1")
position = Vector2i(-112, -272)
rotation_degrees = 0
building_stats = ExtResource("2_nbv8r")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 50.0,
"stored_power": 41.6523
}
})

[sub_resource type="Resource" id="Resource_hysg8"]
script = ExtResource("1_tudf1")
position = Vector2i(-112, -240)
rotation_degrees = 0
building_stats = ExtResource("2_nbv8r")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 50.0,
"stored_power": 41.6523
}
})

[sub_resource type="Resource" id="Resource_a618y"]
script = ExtResource("1_tudf1")
position = Vector2i(-112, -208)
rotation_degrees = 0
building_stats = ExtResource("2_nbv8r")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 50.0,
"stored_power": 41.6523
}
})

[sub_resource type="Resource" id="Resource_m4fai"]
script = ExtResource("1_tudf1")
position = Vector2i(-112, -176)
rotation_degrees = 0
building_stats = ExtResource("2_nbv8r")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 50.0,
"stored_power": 41.6523
}
})

[sub_resource type="Resource" id="Resource_5b4pr"]
script = ExtResource("1_tudf1")
position = Vector2i(-112, 16)
rotation_degrees = 270
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_a3woh"]
script = ExtResource("1_tudf1")
position = Vector2i(-112, 48)
rotation_degrees = 270
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_4eo27"]
script = ExtResource("1_tudf1")
position = Vector2i(-112, 80)
rotation_degrees = 270
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_42l45"]
script = ExtResource("1_tudf1")
position = Vector2i(-112, 464)
rotation_degrees = 180
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_hapc7"]
script = ExtResource("1_tudf1")
position = Vector2i(-80, -304)
rotation_degrees = 0
building_stats = ExtResource("2_nbv8r")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 50.0,
"stored_power": 41.6523
}
})

[sub_resource type="Resource" id="Resource_rthtr"]
script = ExtResource("1_tudf1")
position = Vector2i(-80, -272)
rotation_degrees = 0
building_stats = ExtResource("2_nbv8r")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 50.0,
"stored_power": 41.6523
}
})

[sub_resource type="Resource" id="Resource_mh8qs"]
script = ExtResource("1_tudf1")
position = Vector2i(-80, -240)
rotation_degrees = 0
building_stats = ExtResource("2_nbv8r")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 50.0,
"stored_power": 41.6523
}
})

[sub_resource type="Resource" id="Resource_320fm"]
script = ExtResource("1_tudf1")
position = Vector2i(-80, -208)
rotation_degrees = 0
building_stats = ExtResource("2_nbv8r")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 50.0,
"stored_power": 41.6523
}
})

[sub_resource type="Resource" id="Resource_mhjm0"]
script = ExtResource("1_tudf1")
position = Vector2i(-80, -176)
rotation_degrees = 0
building_stats = ExtResource("2_nbv8r")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 50.0,
"stored_power": 41.6523
}
})

[sub_resource type="Resource" id="Resource_lqjx7"]
script = ExtResource("1_tudf1")
position = Vector2i(-80, -144)
rotation_degrees = 0
building_stats = ExtResource("2_nbv8r")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 50.0,
"stored_power": 41.6523
}
})

[sub_resource type="Resource" id="Resource_uc53j"]
script = ExtResource("1_tudf1")
position = Vector2i(-80, 432)
rotation_degrees = 0
building_stats = ExtResource("2_nbv8r")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 50.0,
"stored_power": 41.6523
}
})

[sub_resource type="Resource" id="Resource_eeyn5"]
script = ExtResource("1_tudf1")
position = Vector2i(-80, 464)
rotation_degrees = 180
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_c1fmf"]
script = ExtResource("1_tudf1")
position = Vector2i(-80, 496)
rotation_degrees = 0
building_stats = ExtResource("2_nbv8r")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 50.0,
"stored_power": 41.6523
}
})

[sub_resource type="Resource" id="Resource_v1p4r"]
script = ExtResource("1_tudf1")
position = Vector2i(-48, -304)
rotation_degrees = 0
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_syg8p"]
script = ExtResource("1_tudf1")
position = Vector2i(-48, -272)
rotation_degrees = 0
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_owuyt"]
script = ExtResource("1_tudf1")
position = Vector2i(-48, -240)
rotation_degrees = 0
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_nqy0i"]
script = ExtResource("1_tudf1")
position = Vector2i(-48, -208)
rotation_degrees = 0
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_8kwwk"]
script = ExtResource("1_tudf1")
position = Vector2i(-48, -176)
rotation_degrees = 0
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_agiwr"]
script = ExtResource("1_tudf1")
position = Vector2i(-48, -144)
rotation_degrees = 0
building_stats = ExtResource("4_ytttp")
component_data = Dictionary[Resource, Dictionary]({})

[sub_resource type="Resource" id="Resource_ig3gw"]
script = ExtResource("1_tudf1")
position = Vector2i(-48, 464)
rotation_degrees = 0
building_stats = ExtResource("2_nbv8r")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 50.0,
"stored_power": 41.6522
}
})

[sub_resource type="Resource" id="Resource_maivb"]
script = ExtResource("1_tudf1")
position = Vector2i(-16, -304)
rotation_degrees = 180
building_stats = ExtResource("9_tn5ax")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 25.0,
"stored_power": 25.0
}
})

[sub_resource type="Resource" id="Resource_1emji"]
script = ExtResource("1_tudf1")
position = Vector2i(-16, -272)
rotation_degrees = 180
building_stats = ExtResource("9_tn5ax")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 25.0,
"stored_power": 25.0
}
})

[sub_resource type="Resource" id="Resource_felji"]
script = ExtResource("1_tudf1")
position = Vector2i(-16, -240)
rotation_degrees = 180
building_stats = ExtResource("9_tn5ax")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 25.0,
"stored_power": 25.0
}
})

[sub_resource type="Resource" id="Resource_c2eq7"]
script = ExtResource("1_tudf1")
position = Vector2i(-16, -208)
rotation_degrees = 180
building_stats = ExtResource("9_tn5ax")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 25.0,
"stored_power": 25.0
}
})

[sub_resource type="Resource" id="Resource_dp4gr"]
script = ExtResource("1_tudf1")
position = Vector2i(-16, -176)
rotation_degrees = 180
building_stats = ExtResource("9_tn5ax")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 25.0,
"stored_power": 25.0
}
})

[sub_resource type="Resource" id="Resource_6t81p"]
script = ExtResource("1_tudf1")
position = Vector2i(-16, -144)
rotation_degrees = 180
building_stats = ExtResource("9_tn5ax")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 25.0,
"stored_power": 25.0
}
})

[sub_resource type="Resource" id="Resource_elk41"]
script = ExtResource("1_tudf1")
position = Vector2i(16, -304)
rotation_degrees = 0
building_stats = ExtResource("8_i5kel")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 2000.0,
"stored_power": 1622.37
}
})

[sub_resource type="Resource" id="Resource_b8c43"]
script = ExtResource("1_tudf1")
position = Vector2i(16, -240)
rotation_degrees = 0
building_stats = ExtResource("8_i5kel")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 2000.0,
"stored_power": 1622.37
}
})

[sub_resource type="Resource" id="Resource_u0enb"]
script = ExtResource("1_tudf1")
position = Vector2i(16, -176)
rotation_degrees = 0
building_stats = ExtResource("8_i5kel")
component_data = Dictionary[Resource, Dictionary]({
ExtResource("3_8bejo"): {
"max_power_stored": 2000.0,
"stored_power": 1622.36
}
})

[sub_resource type="Resource" id="Resource_8k00e"]
script = ExtResource("12_r0up7")
resources = Dictionary[int, int]({
7: 235,
14: 248
})

[resource]
script = ExtResource("13_l7iy4")
win_conditions = Array[ExtResource("17_f05h1")]([])
buildings_data = Dictionary[Vector2i, ExtResource("1_tudf1")]({
Vector2i(-784, 80): SubResource("Resource_uu54a"),
Vector2i(-784, 112): SubResource("Resource_fklql"),
Vector2i(-752, 80): SubResource("Resource_qaksm"),
Vector2i(-752, 112): SubResource("Resource_no4bc"),
Vector2i(-752, 144): SubResource("Resource_xbb8n"),
Vector2i(-752, 176): SubResource("Resource_7c357"),
Vector2i(-752, 208): SubResource("Resource_ot7kx"),
Vector2i(-720, 80): SubResource("Resource_7i310"),
Vector2i(-720, 112): SubResource("Resource_c8rgq"),
Vector2i(-720, 144): SubResource("Resource_rwaoi"),
Vector2i(-720, 176): SubResource("Resource_ffqhp"),
Vector2i(-720, 208): SubResource("Resource_do47a"),
Vector2i(-688, 80): SubResource("Resource_xri4q"),
Vector2i(-688, 112): SubResource("Resource_08dp8"),
Vector2i(-688, 208): SubResource("Resource_jw53m"),
Vector2i(-656, 208): SubResource("Resource_18o26"),
Vector2i(-624, 80): SubResource("Resource_j06nk"),
Vector2i(-624, 112): SubResource("Resource_g6whg"),
Vector2i(-624, 176): SubResource("Resource_ucc0a"),
Vector2i(-624, 208): SubResource("Resource_5b0q6"),
Vector2i(-624, 240): SubResource("Resource_7gv38"),
Vector2i(-624, 272): SubResource("Resource_j444j"),
Vector2i(-624, 304): SubResource("Resource_va50t"),
Vector2i(-624, 336): SubResource("Resource_503xr"),
Vector2i(-624, 368): SubResource("Resource_3p6hy"),
Vector2i(-624, 400): SubResource("Resource_kmbwl"),
Vector2i(-624, 432): SubResource("Resource_7pptx"),
Vector2i(-624, 464): SubResource("Resource_xtidg"),
Vector2i(-592, 80): SubResource("Resource_mt3ri"),
Vector2i(-592, 208): SubResource("Resource_5f03g"),
Vector2i(-592, 464): SubResource("Resource_li6nb"),
Vector2i(-560, 208): SubResource("Resource_cbtk1"),
Vector2i(-560, 464): SubResource("Resource_st3vt"),
Vector2i(-528, 16): SubResource("Resource_ffc6d"),
Vector2i(-528, 208): SubResource("Resource_1qjve"),
Vector2i(-528, 464): SubResource("Resource_eqie4"),
Vector2i(-496, 464): SubResource("Resource_1g3ya"),
Vector2i(-464, 464): SubResource("Resource_6qcyr"),
Vector2i(-432, 464): SubResource("Resource_bg7vr"),
Vector2i(-400, 464): SubResource("Resource_hfqbf"),
Vector2i(-368, -304): SubResource("Resource_un7r1"),
Vector2i(-368, -240): SubResource("Resource_2chwe"),
Vector2i(-368, 464): SubResource("Resource_x8qhh"),
Vector2i(-336, 16): SubResource("Resource_wxipc"),
Vector2i(-336, 400): SubResource("Resource_1ryqs"),
Vector2i(-336, 432): SubResource("Resource_dka8w"),
Vector2i(-336, 464): SubResource("Resource_14vk3"),
Vector2i(-304, -304): SubResource("Resource_eswxr"),
Vector2i(-304, -272): SubResource("Resource_t58x5"),
Vector2i(-304, -240): SubResource("Resource_pwcnk"),
Vector2i(-304, -208): SubResource("Resource_xs7mu"),
Vector2i(-304, 16): SubResource("Resource_4nmii"),
Vector2i(-304, 400): SubResource("Resource_86oao"),
Vector2i(-304, 464): SubResource("Resource_2n7j5"),
Vector2i(-272, -304): SubResource("Resource_3xf7j"),
Vector2i(-272, -272): SubResource("Resource_cdwfe"),
Vector2i(-272, -240): SubResource("Resource_mvgjq"),
Vector2i(-272, -208): SubResource("Resource_sig4l"),
Vector2i(-272, -16): SubResource("Resource_abo7a"),
Vector2i(-272, 400): SubResource("Resource_6xd84"),
Vector2i(-272, 464): SubResource("Resource_i7h8q"),
Vector2i(-240, -304): SubResource("Resource_n4ijd"),
Vector2i(-240, -272): SubResource("Resource_ug2rn"),
Vector2i(-240, -240): SubResource("Resource_r2pqv"),
Vector2i(-240, -208): SubResource("Resource_2v30n"),
Vector2i(-240, 400): SubResource("Resource_c75wk"),
Vector2i(-240, 432): SubResource("Resource_lc2dk"),
Vector2i(-240, 464): SubResource("Resource_p8a22"),
Vector2i(-208, -16): SubResource("Resource_feq5p"),
Vector2i(-208, 48): SubResource("Resource_7l1x2"),
Vector2i(-208, 464): SubResource("Resource_wqjcu"),
Vector2i(-176, 16): SubResource("Resource_oxetg"),
Vector2i(-176, 80): SubResource("Resource_rggi8"),
Vector2i(-176, 464): SubResource("Resource_rsfxe"),
Vector2i(-144, -304): SubResource("Resource_7uviv"),
Vector2i(-144, -272): SubResource("Resource_w1xrs"),
Vector2i(-144, -240): SubResource("Resource_hc3gy"),
Vector2i(-144, -208): SubResource("Resource_2pond"),
Vector2i(-144, -176): SubResource("Resource_7dpd5"),
Vector2i(-144, -144): SubResource("Resource_3o2nc"),
Vector2i(-144, -112): SubResource("Resource_soiku"),
Vector2i(-144, -80): SubResource("Resource_48j0d"),
Vector2i(-144, -48): SubResource("Resource_qgy4y"),
Vector2i(-144, -16): SubResource("Resource_h3t0j"),
Vector2i(-144, 16): SubResource("Resource_ebp3b"),
Vector2i(-144, 48): SubResource("Resource_1omrx"),
Vector2i(-144, 80): SubResource("Resource_rb680"),
Vector2i(-144, 464): SubResource("Resource_tubii"),
Vector2i(-112, -304): SubResource("Resource_wgfge"),
Vector2i(-112, -272): SubResource("Resource_bbefk"),
Vector2i(-112, -240): SubResource("Resource_hysg8"),
Vector2i(-112, -208): SubResource("Resource_a618y"),
Vector2i(-112, -176): SubResource("Resource_m4fai"),
Vector2i(-112, 16): SubResource("Resource_5b4pr"),
Vector2i(-112, 48): SubResource("Resource_a3woh"),
Vector2i(-112, 80): SubResource("Resource_4eo27"),
Vector2i(-112, 464): SubResource("Resource_42l45"),
Vector2i(-80, -304): SubResource("Resource_hapc7"),
Vector2i(-80, -272): SubResource("Resource_rthtr"),
Vector2i(-80, -240): SubResource("Resource_mh8qs"),
Vector2i(-80, -208): SubResource("Resource_320fm"),
Vector2i(-80, -176): SubResource("Resource_mhjm0"),
Vector2i(-80, -144): SubResource("Resource_lqjx7"),
Vector2i(-80, 432): SubResource("Resource_uc53j"),
Vector2i(-80, 464): SubResource("Resource_eeyn5"),
Vector2i(-80, 496): SubResource("Resource_c1fmf"),
Vector2i(-48, -304): SubResource("Resource_v1p4r"),
Vector2i(-48, -272): SubResource("Resource_syg8p"),
Vector2i(-48, -240): SubResource("Resource_owuyt"),
Vector2i(-48, -208): SubResource("Resource_nqy0i"),
Vector2i(-48, -176): SubResource("Resource_8kwwk"),
Vector2i(-48, -144): SubResource("Resource_agiwr"),
Vector2i(-48, 464): SubResource("Resource_ig3gw"),
Vector2i(-16, -304): SubResource("Resource_maivb"),
Vector2i(-16, -272): SubResource("Resource_1emji"),
Vector2i(-16, -240): SubResource("Resource_felji"),
Vector2i(-16, -208): SubResource("Resource_c2eq7"),
Vector2i(-16, -176): SubResource("Resource_dp4gr"),
Vector2i(-16, -144): SubResource("Resource_6t81p"),
Vector2i(16, -304): SubResource("Resource_elk41"),
Vector2i(16, -240): SubResource("Resource_b8c43"),
Vector2i(16, -176): SubResource("Resource_u0enb")
})
inventory = SubResource("Resource_8k00e")
toolbar_data = Dictionary[int, ExtResource("14_qqa7k")]({
0: ExtResource("8_i5kel"),
1: ExtResource("2_nbv8r"),
2: ExtResource("4_ytttp"),
3: ExtResource("5_mhld2"),
4: ExtResource("15_ulv85"),
5: ExtResource("10_o7beu"),
6: ExtResource("9_tn5ax"),
7: ExtResource("16_ylmbn"),
8: ExtResource("6_6rvuy"),
9: ExtResource("11_pjky8")
})

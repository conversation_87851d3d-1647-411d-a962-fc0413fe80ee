[gd_scene load_steps=4 format=3 uid="uid://dyo6imvtq80bf"]

[ext_resource type="PackedScene" uid="uid://ms0scm876sot" path="res://Entities/Buildings/Power Network/power_network.tscn" id="1_ebjsl"]
[ext_resource type="PackedScene" uid="uid://c5hbk2wlqmp0g" path="res://Entities/Buildings/Power Network/testing/testing_power_battery.tscn" id="2_pylnd"]
[ext_resource type="PackedScene" uid="uid://c5dgr7sr43otq" path="res://Entities/Buildings/Power Network/testing/testing_power_generator.tscn" id="3_oqiwr"]

[node name="Testing Power Grid" type="Node2D"]

[node name="PowerNetwork" parent="." instance=ExtResource("1_ebjsl")]

[node name="TestingPowerBattery" parent="." instance=ExtResource("2_pylnd")]
position = Vector2(0, -50)

[node name="TestingPowerGenerator" parent="." instance=ExtResource("3_oqiwr")]
position = Vector2(-84, -50)

[node name="TestingPowerGenerator2" parent="." instance=ExtResource("3_oqiwr")]
position = Vector2(-84, 0)
